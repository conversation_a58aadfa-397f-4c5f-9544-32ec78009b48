import { randomBytes, createHash } from 'crypto';

/**
 * Gera um ID único e atômico para transações PIX
 * MANTÉM COMPATIBILIDADE TOTAL com formato anterior para evitar breaking changes
 * Formato: tx_${timestamp}_${random} - EXATAMENTE como o código original
 */
export function generateUniqueTransactionId(): string {
  // MANTÉM O FORMATO ORIGINAL EXATO para compatibilidade total
  return `tx_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
}

/**
 * Gera um ID único mais robusto para novos casos de uso
 * Usa criptografia forte mas mantém formato compatível
 */
export function generateEnhancedTransactionId(): string {
  const timestamp = Date.now().toString(36);
  const randomPart = randomBytes(8).toString('hex');
  const counter = Math.floor(Math.random() * 10000).toString(36);

  return `tx_${timestamp}_${randomPart}_${counter}`;
}

/**
 * Gera uma chave de idempotência robusta baseada nos parâmetros da transação
 */
export function generateIdempotencyKey(params: {
  customerEmail: string;
  amount: number;
  organizationId: string;
  customerDocument?: string;
  description?: string;
  timestamp?: number; // Adicionar timestamp opcional para controle externo
}): string {
  const { customerEmail, amount, organizationId, customerDocument, description, timestamp } = params;

  // Criar um hash dos parâmetros principais para idempotência
  const keyData = {
    email: customerEmail.toLowerCase().trim(),
    amount: Math.round(amount * 100), // Converter para centavos para evitar problemas de precisão
    org: organizationId,
    doc: customerDocument?.replace(/\D/g, '') || '', // Apenas números do documento
    desc: description?.trim() || ''
  };

  // Usar timestamp com granularidade de 5 minutos para permitir retry em caso de falha
  // Aumentado de 1 para 5 minutos para ser mais permissivo com retries
  const timeWindow = Math.floor((timestamp || Date.now()) / (5 * 60 * 1000)); // 5 minutos

  const hash = createHash('sha256')
    .update(JSON.stringify({ ...keyData, timeWindow }))
    .digest('hex')
    .substring(0, 16);

  return `idem_${hash}_${timeWindow}`;
}

/**
 * Valida se um ID de transação é válido
 */
export function isValidTransactionId(id: string): boolean {
  return /^tx_[a-z0-9]+_[a-f0-9]+_[a-z0-9]+$/.test(id);
}
