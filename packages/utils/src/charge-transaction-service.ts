import { db } from '@repo/database';
import { generateUniqueTransactionId, generateIdempotencyKey } from './transaction-id';
import { logger } from '@repo/logs';
import { isFeatureEnabled } from './feature-flags';
import { circuitBreakers } from './circuit-breaker';
import { transactionMonitor } from './transaction-monitoring';

export interface CreateChargeTransactionParams {
  customerEmail: string;
  customerName: string;
  customerPhone?: string;
  customerDocument?: string;
  amount: number;
  organizationId: string;
  description?: string;
  metadata?: Record<string, any>;
  idempotencyKey?: string;
  allowMultiple?: boolean; // Se true, permite múltiplas transações mesmo com mesmos parâmetros
}

export interface ChargeTransactionResult {
  id: string;
  referenceCode: string;
  status: string;
  amount: number;
  customerEmail: string;
  customerName: string;
  createdAt: Date;
  metadata: Record<string, any>;
  isNew: boolean;
  pixCode?: string;
  pixQrCode?: string;
  pixExpiresAt?: string;
}

/**
 * Serviço específico para transações CHARGE (recebimento PIX)
 * Permite múltiplas transações com mesmo cliente/valor para gerar QR codes únicos
 */
export class ChargeTransactionService {
  /**
   * Cria uma transação CHARGE de forma otimizada
   * Permite múltiplas transações para gerar QR codes únicos
   */
  static async createChargeTransaction(
    params: CreateChargeTransactionParams
  ): Promise<ChargeTransactionResult> {
    const startTime = Date.now();
    const {
      customerEmail,
      customerName,
      customerPhone,
      customerDocument,
      amount,
      organizationId,
      description,
      metadata = {},
      idempotencyKey,
      allowMultiple = true
    } = params;

    // Enhanced logging with performance tracking
    const logContext = {
      customerEmail: customerEmail.substring(0, 3) + '***',
      amount,
      organizationId,
      allowMultiple,
      hasCustomerDocument: !!customerDocument,
      hasDescription: !!description,
      hasMetadata: Object.keys(metadata).length > 0
    };

    // Gerar chave de idempotência única baseada em timestamp
    const finalIdempotencyKey = idempotencyKey || generateIdempotencyKey({
      customerEmail,
      amount,
      organizationId,
      customerDocument,
      description,
      timestamp: Date.now() // Adicionar timestamp para garantir unicidade
    });

    logger.info('Creating CHARGE transaction with enhanced service', {
      ...logContext,
      idempotencyKey: finalIdempotencyKey.substring(0, 10) + '***',
      serviceVersion: '2.0',
      featureFlags: {
        detailedLogging: isFeatureEnabled('enableDetailedLogging', organizationId),
        performanceMonitoring: isFeatureEnabled('enablePerformanceMonitoring', organizationId)
      }
    });

    // Use circuit breaker for database operations
    return await circuitBreakers.databaseOperations.execute(async () => {
      return await db.$transaction(async (tx) => {
      // 1. Verificar se já existe transação com mesma chave de idempotência
      const existingByKey = await tx.transaction.findFirst({
        where: {
          metadata: {
            path: ['idempotencyKey'],
            equals: finalIdempotencyKey
          },
          organizationId,
          type: 'CHARGE'
        },
        orderBy: { createdAt: 'desc' }
      });

      if (existingByKey) {
        const duration = Date.now() - startTime;

        logger.info('Found existing CHARGE transaction by idempotency key', {
          transactionId: existingByKey.id,
          idempotencyKey: finalIdempotencyKey.substring(0, 10) + '***',
          duration_ms: duration
        });

        // Record duplicate detection
        transactionMonitor.recordTransactionCreation({
          type: 'CHARGE',
          organizationId,
          duration,
          success: true,
          isDuplicate: true
        });

        return {
          id: existingByKey.id,
          referenceCode: existingByKey.referenceCode || '',
          status: existingByKey.status,
          amount: existingByKey.amount,
          customerEmail: existingByKey.customerEmail,
          customerName: existingByKey.customerName,
          createdAt: existingByKey.createdAt,
          metadata: existingByKey.metadata as Record<string, any>,
          isNew: false,
          pixCode: (existingByKey.metadata as any)?.pixCode,
          pixQrCode: (existingByKey.metadata as any)?.pixQrCode,
          pixExpiresAt: (existingByKey.metadata as any)?.pixExpiresAt
        };
      }

      // 2. Se allowMultiple = false, verificar duplicatas recentes
      if (!allowMultiple) {
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        const existingRecent = await tx.transaction.findFirst({
          where: {
            customerEmail: customerEmail.toLowerCase().trim(),
            amount,
            organizationId,
            type: 'CHARGE',
            status: { in: ['PENDING', 'PROCESSING', 'APPROVED'] },
            createdAt: { gte: fiveMinutesAgo },
            // Only return transactions that have PIX data
            metadata: {
              path: ['pixCode'],
              not: undefined
            }
          },
          orderBy: { createdAt: 'desc' }
        });

        if (existingRecent) {
          logger.info('Found recent CHARGE transaction - returning existing', {
            transactionId: existingRecent.id,
            customerEmail,
            amount,
            hasPixCode: !!(existingRecent.metadata as any)?.pixCode
          });

          return {
            id: existingRecent.id,
            referenceCode: existingRecent.referenceCode || '',
            status: existingRecent.status,
            amount: existingRecent.amount,
            customerEmail: existingRecent.customerEmail,
            customerName: existingRecent.customerName,
            createdAt: existingRecent.createdAt,
            metadata: existingRecent.metadata as Record<string, any>,
            isNew: false,
            pixCode: (existingRecent.metadata as any)?.pixCode,
            pixQrCode: (existingRecent.metadata as any)?.pixQrCode,
            pixExpiresAt: (existingRecent.metadata as any)?.pixExpiresAt
          };
        }
      }

      // 3. Gerar ID único para nova transação
      const referenceCode = generateUniqueTransactionId();

      // 4. Criar nova transação CHARGE
      const newTransaction = await tx.transaction.create({
        data: {
          referenceCode,
          customerName,
          customerEmail: customerEmail.toLowerCase().trim(),
          customerPhone: customerPhone || '',
          customerDocument: customerDocument || '',
          amount,
          status: 'PENDING',
          type: 'CHARGE',
          description: description || 'Pagamento via PIX',
          metadata: {
            ...metadata,
            idempotencyKey: finalIdempotencyKey,
            createdAt: new Date().toISOString(),
            allowMultiple
          },
          organizationId
        }
      });

      const duration = Date.now() - startTime;

      logger.info('Created new CHARGE transaction', {
        transactionId: newTransaction.id,
        referenceCode: newTransaction.referenceCode,
        idempotencyKey: finalIdempotencyKey.substring(0, 10) + '***',
        allowMultiple,
        duration_ms: duration,
        performance: {
          fast: duration < 100,
          acceptable: duration < 500,
          slow: duration >= 500
        }
      });

      // Performance monitoring
      if (isFeatureEnabled('enablePerformanceMonitoring', organizationId)) {
        if (duration > 1000) {
          logger.warn('Slow CHARGE transaction creation detected', {
            transactionId: newTransaction.id,
            duration_ms: duration,
            organizationId
          });
        }
      }

      // Record metrics
      transactionMonitor.recordTransactionCreation({
        type: 'CHARGE',
        organizationId,
        duration,
        success: true,
        isDuplicate: false
      });

      return {
        id: newTransaction.id,
        referenceCode: newTransaction.referenceCode || '',
        status: newTransaction.status,
        amount: newTransaction.amount,
        customerEmail: newTransaction.customerEmail,
        customerName: newTransaction.customerName,
        createdAt: newTransaction.createdAt,
        metadata: newTransaction.metadata as Record<string, any>,
        isNew: true
      };
      });
    }).catch((error) => {
      const duration = Date.now() - startTime;

      // Record failed transaction
      transactionMonitor.recordTransactionCreation({
        type: 'CHARGE',
        organizationId,
        duration,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });

      throw error;
    });
  }

  /**
   * Atualiza uma transação CHARGE com dados PIX de forma atômica
   */
  static async updateChargeWithPixData(
    transactionId: string,
    pixData: {
      externalId?: string;
      pixCode?: string;
      pixQrCode?: string;
      pixExpiresAt?: string;
      providerMetadata?: Record<string, any>;
    }
  ): Promise<void> {
    await db.$transaction(async (tx) => {
      const existingTransaction = await tx.transaction.findUnique({
        where: { id: transactionId }
      });

      if (!existingTransaction) {
        throw new Error(`Transaction ${transactionId} not found`);
      }

      if (existingTransaction.type !== 'CHARGE') {
        throw new Error(`Transaction ${transactionId} is not a CHARGE transaction`);
      }

      // Atualizar apenas se ainda não tiver dados PIX
      if (!(existingTransaction.metadata as any)?.pixCode) {
        await tx.transaction.update({
          where: { id: transactionId },
          data: {
            externalId: pixData.externalId,
            metadata: {
              ...(existingTransaction.metadata as Record<string, any> || {}),
              pixCode: pixData.pixCode,
              pixQrCode: pixData.pixQrCode,
              pixExpiresAt: pixData.pixExpiresAt,
              ...pixData.providerMetadata,
              updatedAt: new Date().toISOString()
            }
          }
        });

        logger.info('Updated CHARGE transaction with PIX data', {
          transactionId,
          hasPixCode: !!pixData.pixCode,
          hasPixQrCode: !!pixData.pixQrCode
        });
      } else {
        logger.info('CHARGE transaction already has PIX data, skipping update', {
          transactionId,
          existingPixCode: !!(existingTransaction.metadata as any)?.pixCode
        });
      }
    });
  }
}
