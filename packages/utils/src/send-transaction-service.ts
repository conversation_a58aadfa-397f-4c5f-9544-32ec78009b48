import { db } from '@repo/database';
import { generateUniqueTransactionId, generateIdempotencyKey } from './transaction-id';
import { logger } from '@repo/logs';
import { isFeatureEnabled } from './feature-flags';
import { circuitBreakers } from './circuit-breaker';
import { transactionMonitor } from './transaction-monitoring';

export interface CreateSendTransactionParams {
  customerEmail: string;
  customerName: string;
  amount: number;
  organizationId: string;
  pixKey: string;
  pixKeyType: string;
  description?: string;
  metadata?: Record<string, any>;
  idempotencyKey?: string;
}

export interface SendTransactionResult {
  id: string;
  referenceCode: string;
  status: string;
  amount: number;
  customerEmail: string;
  customerName: string;
  createdAt: Date;
  metadata: Record<string, any>;
  isNew: boolean;
  pixKey: string;
  pixKeyType: string;
}

/**
 * Serviço específico para transações SEND (transferência PIX)
 * Bloqueia rigorosamente duplicatas para evitar perda de dinheiro
 */
export class SendTransactionService {
  /**
   * Cria uma transação SEND de forma rigorosa
   * Bloqueia duplicatas para proteger contra perda de dinheiro
   */
  static async createSendTransaction(
    params: CreateSendTransactionParams
  ): Promise<SendTransactionResult> {
    const {
      customerEmail,
      customerName,
      amount,
      organizationId,
      pixKey,
      pixKeyType,
      description,
      metadata = {},
      idempotencyKey
    } = params;

    // Gerar chave de idempotência única
    const finalIdempotencyKey = idempotencyKey || generateIdempotencyKey({
      customerEmail,
      amount,
      organizationId,
      customerDocument: pixKey, // Usar PIX key como identificador único
      description
    });

    const startTime = Date.now();
    const logContext = {
      customerEmail: customerEmail.substring(0, 3) + '***',
      amount,
      organizationId,
      pixKey: pixKey.substring(0, 4) + '***',
      pixKeyType,
      hasDescription: !!description,
      hasMetadata: Object.keys(metadata).length > 0
    };

    logger.info('Creating SEND transaction with strict duplicate prevention', {
      ...logContext,
      idempotencyKey: finalIdempotencyKey.substring(0, 10) + '***',
      serviceVersion: '2.0',
      strictMode: true,
      featureFlags: {
        detailedLogging: isFeatureEnabled('enableDetailedLogging', organizationId),
        performanceMonitoring: isFeatureEnabled('enablePerformanceMonitoring', organizationId)
      }
    });

    // Use circuit breaker for database operations
    return await circuitBreakers.databaseOperations.execute(async () => {
      return await db.$transaction(async (tx) => {
      // 1. Verificar se já existe transação com mesma chave de idempotência
      const existingByKey = await tx.transaction.findFirst({
        where: {
          metadata: {
            path: ['idempotencyKey'],
            equals: finalIdempotencyKey
          },
          organizationId,
          type: 'SEND'
        },
        orderBy: { createdAt: 'desc' }
      });

      if (existingByKey) {
        logger.info('Found existing SEND transaction by idempotency key', {
          transactionId: existingByKey.id,
          idempotencyKey: finalIdempotencyKey
        });

        return {
          id: existingByKey.id,
          referenceCode: existingByKey.referenceCode,
          status: existingByKey.status,
          amount: existingByKey.amount,
          customerEmail: existingByKey.customerEmail,
          customerName: existingByKey.customerName,
          createdAt: existingByKey.createdAt,
          metadata: existingByKey.metadata as Record<string, any>,
          isNew: false,
          pixKey: existingByKey.pixKey || '',
          pixKeyType: existingByKey.pixKeyType || ''
        };
      }

      // 2. Verificação rigorosa de duplicatas para SEND
      const existingTransfer = await tx.transaction.findFirst({
        where: {
          customerEmail: customerEmail.toLowerCase().trim(),
          amount,
          organizationId,
          type: 'SEND',
          pixKey,
          pixKeyType,
          status: { in: ['PENDING', 'PROCESSING', 'APPROVED'] }
        },
        orderBy: { createdAt: 'desc' }
      });

      if (existingTransfer) {
        logger.error('Duplicate SEND transaction detected - blocking creation', {
          existingTransactionId: existingTransfer.id,
          customerEmail,
          amount,
          pixKey: pixKey.substring(0, 4) + '***',
          pixKeyType,
          status: existingTransfer.status
        });

        throw new Error(`Duplicate transfer transaction detected. Transaction ID: ${existingTransfer.id}. Status: ${existingTransfer.status}`);
      }

      // 3. Verificação adicional por externalId (se fornecido)
      if (metadata.externalId) {
        const existingByExternalId = await tx.transaction.findFirst({
          where: {
            externalId: metadata.externalId,
            organizationId,
            type: 'SEND'
          }
        });

        if (existingByExternalId) {
          logger.error('Duplicate SEND transaction by external ID - blocking creation', {
            existingTransactionId: existingByExternalId.id,
            externalId: metadata.externalId,
            customerEmail,
            amount
          });

          throw new Error(`Duplicate transfer transaction by external ID: ${metadata.externalId}. Transaction ID: ${existingByExternalId.id}`);
        }
      }

      // 4. Gerar ID único para nova transação
      const referenceCode = generateUniqueTransactionId();

      // 5. Criar nova transação SEND
      const newTransaction = await tx.transaction.create({
        data: {
          referenceCode,
          customerName,
          customerEmail: customerEmail.toLowerCase().trim(),
          amount,
          status: 'PENDING',
          type: 'SEND',
          description: description || 'Transferência PIX',
          pixKey,
          pixKeyType,
          metadata: {
            ...metadata,
            idempotencyKey: finalIdempotencyKey,
            createdAt: new Date().toISOString(),
            strictDuplicatePrevention: true
          },
          organizationId
        }
      });

      const duration = Date.now() - startTime;

      logger.info('Created new SEND transaction with strict duplicate prevention', {
        transactionId: newTransaction.id,
        referenceCode: newTransaction.referenceCode,
        idempotencyKey: finalIdempotencyKey.substring(0, 10) + '***',
        pixKey: pixKey.substring(0, 4) + '***',
        pixKeyType,
        duration_ms: duration,
        performance: {
          fast: duration < 100,
          acceptable: duration < 500,
          slow: duration >= 500
        }
      });

      // Performance monitoring
      if (isFeatureEnabled('enablePerformanceMonitoring', organizationId)) {
        if (duration > 1000) {
          logger.warn('Slow SEND transaction creation detected', {
            transactionId: newTransaction.id,
            duration_ms: duration,
            organizationId
          });
        }
      }

      // Record metrics
      transactionMonitor.recordTransactionCreation({
        type: 'SEND',
        organizationId,
        duration,
        success: true,
        isDuplicate: false
      });

      return {
        id: newTransaction.id,
        referenceCode: newTransaction.referenceCode || '',
        status: newTransaction.status,
        amount: newTransaction.amount,
        customerEmail: newTransaction.customerEmail,
        customerName: newTransaction.customerName,
        createdAt: newTransaction.createdAt,
        metadata: newTransaction.metadata as Record<string, any>,
        isNew: true,
        pixKey: newTransaction.pixKey || '',
        pixKeyType: newTransaction.pixKeyType || ''
      };
      });
    }).catch((error) => {
      const duration = Date.now() - startTime;

      // Record failed transaction
      transactionMonitor.recordTransactionCreation({
        type: 'SEND',
        organizationId,
        duration,
        success: false,
        error: error instanceof Error ? error.message : String(error)
      });

      throw error;
    });
  }

  /**
   * Verifica se uma transferência já existe antes de criar
   * Útil para validação prévia
   */
  static async checkDuplicateTransfer(params: {
    customerEmail: string;
    amount: number;
    organizationId: string;
    pixKey: string;
    pixKeyType: string;
  }): Promise<{
    exists: boolean;
    transactionId?: string;
    status?: string;
  }> {
    const { customerEmail, amount, organizationId, pixKey, pixKeyType } = params;

    const existingTransfer = await db.transaction.findFirst({
      where: {
        customerEmail: customerEmail.toLowerCase().trim(),
        amount,
        organizationId,
        type: 'SEND',
        pixKey,
        pixKeyType,
        status: { in: ['PENDING', 'PROCESSING', 'APPROVED'] }
      },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        status: true,
        createdAt: true
      }
    });

    return {
      exists: !!existingTransfer,
      transactionId: existingTransfer?.id,
      status: existingTransfer?.status
    };
  }

  /**
   * Lista transferências duplicadas para análise
   * Otimizada para performance em produção
   */
  static async findDuplicateTransfers(organizationId: string, limit: number = 10): Promise<Array<{
    id: string;
    customerEmail: string;
    amount: number;
    pixKey: string;
    pixKeyType: string;
    status: string;
    createdAt: Date;
    duplicateCount: number;
  }>> {
    // Limitar busca aos últimos 30 dias para performance
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    const duplicates = await db.$queryRaw`
      SELECT
        t1.id,
        t1."customerEmail",
        t1.amount,
        t1."pixKey",
        t1."pixKeyType",
        t1.status,
        t1."createdAt",
        COUNT(*) OVER (
          PARTITION BY t1."customerEmail", t1.amount, t1."pixKey", t1."pixKeyType"
        ) as "duplicateCount"
      FROM "transaction" t1
      WHERE t1."organizationId" = ${organizationId}
        AND t1.type = 'SEND'
        AND t1.status IN ('PENDING', 'PROCESSING', 'APPROVED')
        AND t1."createdAt" >= ${thirtyDaysAgo}
      ORDER BY t1."createdAt" DESC
      LIMIT ${Math.min(limit, 100)} -- Máximo de 100 registros para segurança
    `;

    // Filtrar apenas duplicatas reais
    const filteredDuplicates = (duplicates as any[]).filter(d => d.duplicateCount > 1);

    return filteredDuplicates as Array<{
      id: string;
      customerEmail: string;
      amount: number;
      pixKey: string;
      pixKeyType: string;
      status: string;
      createdAt: Date;
      duplicateCount: number;
    }>;
  }
}
