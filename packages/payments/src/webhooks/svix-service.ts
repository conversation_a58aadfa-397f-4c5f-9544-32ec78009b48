import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { svixService, EVENT_TYPE_MAPPING } from "@repo/utils";
import { transaction } from "@prisma/client";

// Default SVIX App ID from environment
const DEFAULT_SVIX_APP_ID = process.env.SVIX_APP_ID || "app_2xJGtEh9B3sOptpW4thRObvtlh9";

/**
 * Gets or creates a SVIX configuration
 */
export async function getSvixConfig() {
  try {
    // Try to get existing config
    let config = await db.svix_config.findFirst({
      where: { enabled: true },
      orderBy: { updatedAt: "desc" },
    });

    // If no config exists, create one
    if (!config) {
      config = await db.svix_config.create({
        data: {
          id: `svix_config_${Date.now()}`,
          appId: DEFAULT_SVIX_APP_ID,
          appName: "Pluggou PIX",
          enabled: true,
          updatedAt: new Date(),
        },
      });
      logger.info("Created SVIX config", { appId: config.appId });
    }

    return config;
  } catch (error) {
    // Safely handle error logging
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined
    };

    logger.error("Failed to get or create SVIX config", errorInfo);
    throw error;
  }
}

/**
 * Creates a webhook endpoint in SVIX
 */
export async function createSvixWebhookEndpoint(webhook: any) {
  try {
    // Validate webhook object
    if (!webhook || !webhook.id || !webhook.url || !webhook.organizationId) {
      logger.error("Invalid webhook object provided to createSvixWebhookEndpoint", { webhook });
      throw new Error("Invalid webhook object: missing required fields");
    }

    // Ensure events is an array
    if (!webhook.events || !Array.isArray(webhook.events)) {
      logger.warn("Webhook has no events or events is not an array", { webhookId: webhook.id });
      webhook.events = [];
    }

    // Ensure secret exists
    if (!webhook.secret) {
      logger.warn("Webhook has no secret, generating one", { webhookId: webhook.id });
      webhook.secret = `whsec_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;

      // Update the webhook with the new secret
      await db.webhook.update({
        where: { id: webhook.id },
        data: { secret: webhook.secret },
      });
    }

    const config = await getSvixConfig();
    logger.info("Got SVIX config", {
      appId: config.appId,
      webhookId: webhook.id,
      webhookUrl: webhook.url
    });

    // Map event types - check if they're already in SVIX format
    const filterTypes = webhook.events.map((event: string) => {
      // If event is already in SVIX format (contains dot and capital letters), use as is
      if (event.includes('.') && /[A-Z]/.test(event)) {
        // Check if this event exists in our mapping (for correction of wrong names)
        const correctedEvent = EVENT_TYPE_MAPPING[event as keyof typeof EVENT_TYPE_MAPPING];
        if (correctedEvent) {
          logger.info("Correcting event name", {
            original: event,
            corrected: correctedEvent,
            webhookId: webhook.id
          });
          return correctedEvent;
        }
        return event;
      }
      // Otherwise, map from internal format to SVIX format
      const mappedType = EVENT_TYPE_MAPPING[event as keyof typeof EVENT_TYPE_MAPPING] || event;
      return mappedType;
    }).filter(Boolean); // Remove any undefined or null values

    logger.info("Event mapping details", {
      originalEvents: webhook.events,
      mappedEvents: filterTypes,
      webhookId: webhook.id
    });

    // Validate that we have valid events
    if (filterTypes.length === 0) {
      logger.warn("No valid events found after mapping, using empty array (will receive all events)", {
        webhookId: webhook.id,
        originalEvents: webhook.events
      });
    }

    logger.info("Creating SVIX endpoint", {
      webhookId: webhook.id,
      url: webhook.url,
      eventsCount: webhook.events.length,
      filterTypesCount: filterTypes.length,
      filterTypes
    });

    let endpointId: string;

    // Strategy 1: Try with organization-specific channel and mapped events
    try {
      endpointId = await svixService.createEndpoint(
        config.appId,
        webhook.url,
        `Webhook for organization ${webhook.organizationId}`,
        filterTypes,
        webhook.organizationId
      );

      logger.info("Created SVIX endpoint with organization channel", {
        webhookId: webhook.id,
        endpointId,
        strategy: "org-channel-mapped-events"
      });

    } catch (error) {
      if (error instanceof Error && error.message.includes('422')) {
        logger.warn("Strategy 1 failed (422 error), trying fallback strategies", {
          webhookId: webhook.id,
          error: error.message
        });

        // Strategy 2: Try with organization channel but without filterTypes
        try {
          endpointId = await svixService.createEndpoint(
            config.appId,
            webhook.url,
            `Webhook for organization ${webhook.organizationId}`,
            [], // No filterTypes - will receive all events for this org
            webhook.organizationId // Keep organization channel for isolation
          );

          logger.info("Created SVIX endpoint with organization channel but no filterTypes", {
            webhookId: webhook.id,
            endpointId,
            strategy: "org-channel-no-filter-types"
          });

        } catch (error2) {
          if (error2 instanceof Error && error2.message.includes('422')) {
            logger.warn("Strategy 2 failed (422 error), trying without filterTypes", {
              webhookId: webhook.id,
              error: error2.message
            });

            // Strategy 3: Try with basic events only but keep organization channel
            try {
              const basicEvents = ['Transaction.Created', 'Transaction.Updated'];
              endpointId = await svixService.createEndpoint(
                config.appId,
                webhook.url,
                `Webhook for organization ${webhook.organizationId}`,
                basicEvents,
                webhook.organizationId // Keep organization channel for isolation
              );

              logger.info("Created SVIX endpoint with basic events and organization channel", {
                webhookId: webhook.id,
                endpointId,
                strategy: "basic-events-with-org-channel"
              });

            } catch (error3) {
              logger.error("All SVIX endpoint creation strategies failed - cannot create endpoint without organization isolation", {
                webhookId: webhook.id,
                url: webhook.url,
                organizationId: webhook.organizationId,
                finalError: error3 instanceof Error ? error3.message : String(error3),
                note: "Refusing to create endpoint without organization channel to prevent cross-organization webhook leakage"
              });
              throw new Error(`Failed to create webhook endpoint for organization ${webhook.organizationId}: ${error3 instanceof Error ? error3.message : String(error3)}`);
            }
          } else {
            throw error2; // Re-throw non-422 errors
          }
        }
      } else {
        throw error; // Re-throw non-422 errors
      }
    }

    const endpoint = {
      id: endpointId,
      url: webhook.url,
      description: `Webhook for organization ${webhook.organizationId}`,
      filterTypes,
      disabled: false,
    };

    if (!endpoint) {
      throw new Error("Failed to create SVIX endpoint: null response");
    }

    // Verify that the endpoint was created with organization channel
    const svix = svixService.getClient();
    const createdEndpoint = await svix.endpoint.get(config.appId, endpointId);

    if (!createdEndpoint.channels || !createdEndpoint.channels.includes(`org-${webhook.organizationId}`)) {
      logger.error("CRITICAL: Endpoint created without organization channel - potential security issue", {
        webhookId: webhook.id,
        endpointId,
        organizationId: webhook.organizationId,
        endpointChannels: createdEndpoint.channels
      });

      // Delete the problematic endpoint
      await svix.endpoint.delete(config.appId, endpointId);
      throw new Error(`Security violation: Endpoint created without organization channel for org ${webhook.organizationId}`);
    }

    logger.info("Successfully created SVIX endpoint with organization channel verified", {
      webhookId: webhook.id,
      endpointId: endpoint.id,
      organizationId: webhook.organizationId,
      channels: createdEndpoint.channels
    });

    // Update webhook with SVIX endpoint ID
    await db.webhook.update({
      where: { id: webhook.id },
      data: {
        svixEndpointId: endpoint.id,
        useSvix: true,
      },
    });

    logger.info("Created SVIX webhook endpoint", {
      webhookId: webhook.id,
      endpointId: endpoint.id,
    });

    return endpoint;
  } catch (error) {
    logger.error("Failed to create SVIX webhook endpoint", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      webhookId: webhook?.id || 'unknown',
      webhookUrl: webhook?.url || 'unknown',
    });

    // Continue without SVIX integration if it fails
    if (webhook && webhook.id) {
      try {
        await db.webhook.update({
          where: { id: webhook.id },
          data: {
            useSvix: false,
            svixEndpointId: null,
          },
        });
        logger.info("Disabled SVIX integration for webhook due to error", { webhookId: webhook.id });
      } catch (updateError) {
        logger.error("Failed to update webhook after SVIX error", {
          webhookId: webhook.id,
          error: updateError
        });
      }
    }

    throw error;
  }
}

/**
 * Updates a webhook endpoint in SVIX
 */
export async function updateSvixWebhookEndpoint(webhook: any) {
  try {
    if (!webhook.svixEndpointId) {
      logger.warn("Webhook has no SVIX endpoint ID", { webhookId: webhook.id });
      return await createSvixWebhookEndpoint(webhook);
    }

    const config = await getSvixConfig();

    // Update endpoint in SVIX
    await svixService.updateEndpoint(
      config.appId,
      webhook.svixEndpointId,
      {
        url: webhook.url,
        description: `Webhook for organization ${webhook.organizationId}`,
        events: webhook.events.map((event: string) => EVENT_TYPE_MAPPING[event as keyof typeof EVENT_TYPE_MAPPING] || event),
        disabled: !webhook.isActive,
      }
    );

    logger.info("Updated SVIX webhook endpoint", {
      webhookId: webhook.id,
      endpointId: webhook.svixEndpointId,
    });

    return {
      id: webhook.svixEndpointId,
      url: webhook.url,
      description: `Webhook for organization ${webhook.organizationId}`,
      disabled: !webhook.isActive,
    };
  } catch (error) {
    logger.error("Failed to update SVIX webhook endpoint", {
      error,
      webhookId: webhook.id,
    });
    throw error;
  }
}

/**
 * Deletes a webhook endpoint from SVIX
 */
export async function deleteSvixWebhookEndpoint(webhook: any) {
  try {
    if (!webhook.svixEndpointId) {
      logger.warn("Webhook has no SVIX endpoint ID, skipping SVIX deletion", {
        webhookId: webhook.id
      });
      return;
    }

    const config = await getSvixConfig();

    // Delete endpoint from SVIX
    await svixService.deleteEndpoint(config.appId, webhook.svixEndpointId);

    logger.info("Deleted SVIX webhook endpoint", {
      webhookId: webhook.id,
      endpointId: webhook.svixEndpointId,
      organizationId: webhook.organizationId,
    });

  } catch (error) {
    logger.error("Failed to delete SVIX webhook endpoint", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      webhookId: webhook?.id || 'unknown',
      svixEndpointId: webhook?.svixEndpointId || 'unknown',
    });
    throw error;
  }
}

/**
 * Sends a transaction event to SVIX
 */
export async function sendTransactionEventToSvix(
  transaction: transaction,
  eventType: string,
  previousStatus?: string
) {
  try {
    const config = await getSvixConfig();

    // Prepare transaction data
    const transactionData = {
      id: transaction.id,
      externalId: transaction.externalId,
      referenceCode: transaction.referenceCode,
      endToEndId: transaction.endToEndId,
      amount: transaction.amount,
      status: transaction.status,
      type: transaction.type,
      customerName: transaction.customerName,
      customerEmail: transaction.customerEmail,
      customerDocument: transaction.customerDocument,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      paymentAt: transaction.paymentAt,
      organizationId: transaction.organizationId,
    };

    // Use standardized webhook format
    const eventData = {
      id: `evt_${Date.now()}_${transaction.id}`,
      type: eventType,
      created_at: new Date().toISOString(),
      data: transactionData,
    };

    // Send event to SVIX with organization-specific channel
    const messageId = await svixService.sendEvent(
      config.appId,
      eventType,
      eventData,
      transaction.organizationId
    );

    const message = { id: messageId };

    // Create webhook event in database
    const webhookEvent = await db.webhook_event.create({
      data: {
        type: eventType,
        payload: transactionData, // Store only the transaction data, not the wrapper
        transactionId: transaction.id,
        organizationId: transaction.organizationId,
        svixMessageId: message.id,
        svixEventType: EVENT_TYPE_MAPPING[eventType as keyof typeof EVENT_TYPE_MAPPING] || eventType,
      },
    });

    logger.info("Sent transaction event to SVIX", {
      transactionId: transaction.id,
      eventType,
      messageId: message.id,
      webhookEventId: webhookEvent.id,
    });

    return { message, webhookEvent };
  } catch (error) {
    // Safely handle error logging
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined,
      transactionId: transaction.id,
      eventType
    };

    logger.error("Failed to send transaction event to SVIX", errorInfo);
    throw error;
  }
}

/**
 * Migrates existing webhooks to SVIX
 */
export async function migrateWebhooksToSvix() {
  try {
    // Get all webhooks that don't have a SVIX endpoint ID
    const webhooks = await db.webhook.findMany({
      where: {
        svixEndpointId: null,
        isActive: true,
      },
    });

    logger.info(`Migrating ${webhooks.length} webhooks to SVIX`);

    // Create SVIX endpoints for each webhook
    for (const webhook of webhooks) {
      try {
        await createSvixWebhookEndpoint(webhook);
      } catch (error) {
        logger.error("Failed to migrate webhook to SVIX", {
          error,
          webhookId: webhook.id,
        });
      }
    }

    logger.info("Completed webhook migration to SVIX");
  } catch (error) {
    logger.error("Failed to migrate webhooks to SVIX", { error });
    throw error;
  }
}
