import { db } from "@repo/database";

/**
 * Interface para definir os tipos de taxa
 */
export interface TransactionFees {
  percentFee: number;
  fixedFee: number;
  totalFee: number;
  source?: 'organization' | 'gateway' | 'default';
}

/**
 * Tipos de transações para diferentes cálculos de taxa
 */
export type TransactionFeeType = 'CHARGE' | 'TRANSFER';

/**
 * Calcula as taxas de uma transação com base nas configurações da organização
 *
 * @param organizationId ID da organização
 * @param amount Valor da transação em centavos
 * @param type Tipo de transação (cobrança ou transferência)
 * @param gatewayId ID opcional do gateway a ser usado para taxas padrão
 * @returns Objeto com as taxas calculadas
 */
export async function calculateTransactionFees(
  organizationId: string,
  amount: number,
  type: TransactionFeeType,
  gatewayId?: string
): Promise<TransactionFees> {
  // 🚀 PRIORIDADE 1: SEMPRE usar taxas da organização se existirem
  const taxes = await db.organization_taxes.findUnique({
    where: { organizationId }
  });

  // Se encontrou taxas da organização, usar elas (PRIORIDADE MÁXIMA)
  if (taxes) {
    console.log(`Using organization taxes for ${organizationId}: ${taxes.pixChargePercentFee}% + R$ ${taxes.pixChargeFixedFee}`);

    // Calcular com base no tipo de transação usando as taxas da organização
    if (type === 'CHARGE') {
      const percentFee = (amount * taxes.pixChargePercentFee) / 100;
      const fixedFee = taxes.pixChargeFixedFee;

      // Calcular o total das taxas
      let totalFee = percentFee + fixedFee;

      // Garantir que as taxas não excedam o valor da transação (máximo 99% do valor)
      if (totalFee >= amount) {
        totalFee = amount * 0.99; // Limitar a 99% do valor da transação
      }

      return {
        percentFee,
        fixedFee,
        totalFee,
        source: 'organization'
      };
    } else {
      const percentFee = (amount * taxes.pixTransferPercentFee) / 100;
      const fixedFee = taxes.pixTransferFixedFee;

      // Calcular o total das taxas
      let totalFee = percentFee + fixedFee;

      // Para transferências PIX, não limitamos a taxa pelo valor da transação
      // pois a taxa é cobrada ALÉM do valor transferido, não deduzida dele
      // A limitação de 99% só se aplica a transações de cobrança (CHARGE)

      return {
        percentFee,
        fixedFee,
        totalFee,
        source: 'organization'
      };
    }
  }

  // 🚀 PRIORIDADE 2: Se não encontrou taxas da organização, buscar taxas do gateway
  console.log(`No organization taxes found for ${organizationId}, looking for gateway fees`);

  // Se temos um gatewayId específico, buscar esse gateway
  let gateway = null;

    if (gatewayId) {
      gateway = await db.payment_gateway.findUnique({
        where: { id: gatewayId }
      });
    }

    // Se não temos um gateway específico, buscar gateway global padrão
    if (!gateway) {
      gateway = await db.payment_gateway.findFirst({
        where: {
          isGlobal: true,
          isActive: true,
          isDefault: true
        }
      });
    }


    // Se encontramos um gateway, usar suas taxas
    if (gateway) {
      console.log(`Using fees from gateway ${gateway.id} (${gateway.type}) for organization ${organizationId}`);

      if (type === 'CHARGE') {
        const percentFee = (amount * (Number(gateway.pixChargePercentFee) || 0)) / 100;
        const fixedFee = gateway.pixChargeFixedFee?.toNumber() || 0;

        // Calcular o total das taxas
        let totalFee = Number(percentFee) + Number(fixedFee);

        // Garantir que as taxas não excedam o valor da transação (máximo 99% do valor)
        if (totalFee >= amount) {
          totalFee = amount * 0.99; // Limitar a 99% do valor da transação
        }

        return {
          percentFee: Number(percentFee),
          fixedFee: Number(fixedFee),
          totalFee: Number(totalFee),
          source: 'gateway'
        };
      } else {
        const percentFee = (amount * (Number(gateway.pixTransferPercentFee) || 0)) / 100;
        const fixedFee = gateway.pixTransferFixedFee?.toNumber() || 0;

        // Calcular o total das taxas
        let totalFee = Number(percentFee) + Number(fixedFee);

        // Para transferências PIX, não limitamos a taxa pelo valor da transação
        // pois a taxa é cobrada ALÉM do valor transferido, não deduzida dele
        // A limitação de 99% só se aplica a transações de cobrança (CHARGE)

        return {
          percentFee: Number(percentFee),
          fixedFee: Number(fixedFee),
          totalFee: Number(totalFee),
          source: 'gateway'
        };
      }
    }

  // Se não encontramos nenhum gateway, usar taxas zero
  console.log(`No gateway found for organization ${organizationId}, using zero fees`);
  return {
    percentFee: 0,
    fixedFee: 0,
    totalFee: 0,
    source: 'default'
  };
}

/**
 * Verifica se a organização pode processar transações baseado no status
 *
 * @param organizationId ID da organização
 * @returns Objeto indicando se pode processar e mensagem de erro se não
 */
export async function canProcessTransaction(
  organizationId: string
): Promise<{ canProcess: boolean; message?: string }> {
  const organization = await db.organization.findUnique({
    where: { id: organizationId },
    select: {
      id: true,
      status: true
    }
  });

  if (!organization) {
    return {
      canProcess: false,
      message: 'Organization not found'
    };
  }

  // Verificar status
  if (organization.status !== "APPROVED") {
    return {
      canProcess: false,
      message: organization.status === "PENDING_REVIEW"
        ? 'Organization is pending review'
        : organization.status === "REJECTED"
        ? 'Organization was rejected'
        : 'Organization is blocked'
    };
  }

  return { canProcess: true };
}
