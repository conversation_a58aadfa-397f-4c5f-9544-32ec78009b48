import { logger } from "@repo/logs";
import * as reflowpay from "./reflowpay";
import * as primepag from "./primepag";

import * as pixium from "./pixium";
import * as transfeera from "./transfeera";
import * as mediuspag from "./mediuspag";
import * as zendry from "./zendry";
import * as mocksim from "./mocksim";
import * as pluggouPix from "./pluggou-pix";
import * as flow2pay from "./flow2pay";
import * as ecomovi from "./ecomovi";
import * as cartwave from "./cartwave";
import * as pagarme from "./pagarme";
import * as owempay from "./owempay";
import * as owempayV2 from "./owempayv2";
import * as zeitbank from "./zeitbank";
import * as xdpag from "./xdpag";
import { db } from "@repo/database";
import { config } from 'dotenv';

// Cache for gateway credentials to reduce database queries
interface CacheEntry {
  credentials: Record<string, any>;
  timestamp: number;
}

const credentialsCache = new Map<string, CacheEntry>();
const CACHE_TTL = 60 * 1000; // 1 minuto (reduzido para facilitar testes e evitar problemas de cache)

// Force reload environment variables from .env file
function reloadEnvVars() {
  try {
    // Try to reload from .env file
    config({ path: '.env', override: true });
    logger.info('🔄 Environment variables reloaded from .env file');
  } catch (error) {
    logger.warn('⚠️ Could not reload .env file, using existing env vars', { error });
  }
}

// Helper to get credentials from environment variables
function getEnvironmentCredentials(gatewayType: string): Record<string, any> | null {
  const prefix = `${gatewayType}_`;

  switch (gatewayType) {
    case 'PLUGGOU_PIX':
      return {
        apiKey: process.env.PLUGGOU_PIX_API_KEY,
        apiUrl: process.env.PLUGGOU_PIX_API_URL,
        environment: process.env.PLUGGOU_PIX_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.PLUGGOU_PIX_WEBHOOK_SECRET,
      };

    case 'FLOW2PAY':
      return {
        clientId: process.env.FLOW2PAY_CLIENT_ID,
        clientSecret: process.env.FLOW2PAY_CLIENT_SECRET,
        eventToken: process.env.FLOW2PAY_EVENT_TOKEN,
        apiUrl: process.env.FLOW2PAY_API_URL,
        environment: process.env.FLOW2PAY_ENVIRONMENT || 'sandbox',
        timeout: process.env.FLOW2PAY_TIMEOUT ? parseInt(process.env.FLOW2PAY_TIMEOUT) : undefined,
        retries: process.env.FLOW2PAY_RETRIES ? parseInt(process.env.FLOW2PAY_RETRIES) : undefined,
      };

    case 'TRANSFEERA':
      return {
        clientId: process.env.TRANSFEERA_CLIENT_ID,
        clientSecret: process.env.TRANSFEERA_CLIENT_SECRET,
        environment: process.env.TRANSFEERA_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.TRANSFEERA_WEBHOOK_SECRET,
      };

    case 'PIXIUM':
      return {
        apiKey: process.env.PIXIUM_API_KEY,
        apiSecret: process.env.PIXIUM_API_SECRET,
        environment: process.env.PIXIUM_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.PIXIUM_WEBHOOK_SECRET,
      };

    case 'REFLOWPAY':
      return {
        apiKey: process.env.REFLOWPAY_API_KEY,
        apiSecret: process.env.REFLOWPAY_API_SECRET,
        environment: process.env.REFLOWPAY_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.REFLOWPAY_WEBHOOK_SECRET,
      };

    case 'PRIMEPAG':
      return {
        apiKey: process.env.PRIMEPAG_API_KEY,
        apiSecret: process.env.PRIMEPAG_API_SECRET,
        environment: process.env.PRIMEPAG_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.PRIMEPAG_WEBHOOK_SECRET,
      };

    case 'MEDIUSPAG':
      return {
        apiKey: process.env.MEDIUSPAG_API_KEY,
        apiSecret: process.env.MEDIUSPAG_API_SECRET,
        environment: process.env.MEDIUSPAG_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.MEDIUSPAG_WEBHOOK_SECRET,
      };

    case 'ZENDRY':
      return {
        clientId: process.env.ZENDRY_CLIENT_ID,
        clientSecret: process.env.ZENDRY_CLIENT_SECRET,
        environment: process.env.ZENDRY_ENVIRONMENT || 'production',
        // Note: secretKey será fornecido pela Zendry junto com as credenciais para validação MD5 dos webhooks
        // webhookSecret é opcional e usado para validação adicional se configurado
        webhookSecret: process.env.ZENDRY_WEBHOOK_SECRET,
      };

    case 'MOCKSIM':
      return {
        apiKey: process.env.MOCKSIM_API_KEY || 'mock_key',
        apiSecret: process.env.MOCKSIM_API_SECRET || 'mock_secret',
        environment: 'sandbox',
      };

    case 'ECOMOVI':
      return {
        clientId: process.env.ECOMOVI_CLIENT_ID,
        clientSecret: process.env.ECOMOVI_CLIENT_SECRET,
        pixKey: process.env.ECOMOVI_PIX_KEY,
        environment: process.env.ECOMOVI_ENVIRONMENT || 'production',
        // Certificados específicos para PIX IN (Cash IN) e PIX OUT (Cash OUT)
        cashInCert: process.env.ECOMOVI_CASH_IN_CERT?.replace(/\\n/g, '\n'),
        cashInKey: process.env.ECOMOVI_CASH_IN_KEY?.replace(/\\n/g, '\n'),
        cashOutCert: process.env.ECOMOVI_CASH_OUT_CERT?.replace(/\\n/g, '\n'),
        cashOutKey: process.env.ECOMOVI_CASH_OUT_KEY?.replace(/\\n/g, '\n'),
        // URLs das APIs
        authUrl: process.env.ECOMOVI_AUTH_URL,
        cashInUrl: process.env.ECOMOVI_CASH_IN_URL,
        cashOutUrl: process.env.ECOMOVI_CASH_OUT_URL,
        // Webhook secret (se disponível)
        webhookSecret: process.env.ECOMOVI_WEBHOOK_SECRET,
      };

    case 'CARTWAVE':
      return {
        apiKey: process.env.CARTWAVEHUB_KEY,
        environment: process.env.CARTWAVE_ENVIRONMENT || 'production',
        webhookSecret: process.env.CARTWAVE_WEBHOOK_SECRET,
      };
    case 'PAGARME':
      return {
        apiKey: process.env.PAGARME_API_KEY,
        environment: process.env.PAGARME_ENVIRONMENT || 'production',
        webhookSecret: process.env.PAGARME_WEBHOOK_SECRET,
      };

    case 'OWEMPAY':
      return {
        clientId: process.env.OWEMPAY_CLIENT_ID,
        clientSecret: process.env.OWEMPAY_CLIENT_SECRET,
        email: process.env.OWEMPAY_EMAIL,
        password: process.env.OWEMPAY_PASSWORD,
        environment: process.env.OWEMPAY_ENVIRONMENT || 'production',
        webhookSecret: process.env.OWEMPAY_WEBHOOK_SECRET,
      };

    case 'OWEMPAY_V2':
      return {
        apiKey: process.env.OWEMPAY_V2_API_KEY,
        apiSecret: process.env.OWEMPAY_V2_API_SECRET,
        environment: process.env.OWEMPAY_V2_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.OWEMPAY_V2_WEBHOOK_SECRET,
        accountId: process.env.OWEMPAY_V2_ACCOUNT_ID || '************',
      };

    case 'ZEITBANK':
      return {
        // Credenciais de PIX (QRCODES API)
        clientId: process.env.ZEITBANK_CLIENT_ID,
        clientSecret: process.env.ZEITBANK_CLIENT_SECRET,
        pixKey: process.env.ZEITBANK_PIX_KEY,
        environment: process.env.ZEITBANK_ENVIRONMENT || 'production',

        // Credenciais de Transferência (ACCOUNTS API)
        transferClientId: process.env.ZEITBANK_TRANSFER_CLIENT_ID,
        transferClientSecret: process.env.ZEITBANK_TRANSFER_CLIENT_SECRET,

        // URLs das APIs
        pixApiUrl: process.env.ZEITBANK_PIX_API_URL || 'https://api.pix.tycoontech.io',
        contaApiUrl: process.env.ZEITBANK_CONTA_API_URL || 'https://conta-zeit.tycoontech.io',
        authUrl: process.env.ZEITBANK_AUTH_URL || 'https://api.pix.tycoontech.io',

        // Webhook secret (compartilhado entre instâncias)
        webhookSecret: process.env.ZEITBANK_WEBHOOK_SECRET,

        // Certificados para produção (Vercel) - compartilhados entre instâncias
        certContent: process.env.ZEITBANK_CERT_CONTENT,
        keyContent: process.env.ZEITBANK_KEY_CONTENT,
        accountsCertContent: process.env.ZEITBANK_ACCOUNTS_CERT_CONTENT,
        accountsKeyContent: process.env.ZEITBANK_ACCOUNTS_KEY_CONTENT,

        // Certificados para desenvolvimento local (deprecated)
        certPath: process.env.ZEITBANK_CERT_PATH,
        keyPath: process.env.ZEITBANK_KEY_PATH,
      };

    case 'XDPAG':
      return {
        username: process.env.XDPAG_USERNAME,
        password: process.env.XDPAG_PASSWORD,
        environment: process.env.XDPAG_ENVIRONMENT || 'sandbox',
        webhookSecret: process.env.XDPAG_WEBHOOK_SECRET,
      };

    default:
      logger.warn(`No environment credential mapping for gateway type: ${gatewayType}`);
      return null;
  }
}

// Common interface for payment gateway operations
export interface PaymentGatewayProvider {
  createPixPayment(params: {
    amount: number;
    customerName: string;
    customerEmail: string;
    customerPhone?: string;
    customerDocument?: string;
    customerDocumentType?: string;
    description?: string;
    postbackUrl?: string;
    organizationId: string;
    externalCode?: string;
    metadata?: Record<string, any>;
    idempotencyKey?: string;
  }): Promise<any>;

  processPixWithdrawal(params: {
    amount: number;
    pixKey: string;
    pixKeyType: string;
    postbackUrl?: string;
    organizationId: string;
    transactionId?: string; // Optional transaction ID for better tracking
  }): Promise<any>;

  getTransactionStatus(params: {
    transactionId: string;
    organizationId: string;
    transactionType?: 'CHARGE' | 'SEND';
  }): Promise<any>;

  processRefund(params: {
    transactionId: string;
    amount: number;
    reason?: string;
    organizationId: string;
  }): Promise<any>;
}

// Factory to get the appropriate provider based on gateway type
export async function getPaymentProvider(organizationId: string, options?: {
  forceType?: string;
  preferredType?: string;
  action?: 'charge' | 'withdrawal' | 'refund' | 'status';
}) {
  // Log provider selection (production-safe)
  logger.info('getPaymentProvider called', {
    organizationId,
    options,
    timestamp: new Date().toISOString()
  });

  logger.info(`🔍 DEBUG: getPaymentProvider called`, {
    organizationId,
    options,
    timestamp: new Date().toISOString()
  });

  try {
    // 🔄 FORCE RELOAD environment variables to ensure latest values
    reloadEnvVars();

    // DEBUG: Log environment variables AFTER reload
    logger.info(`🔍 DEBUG: Environment variables check (after reload)`, {
      organizationId,
      DEFAULT_GATEWAY_CHARGE: process.env.DEFAULT_GATEWAY_CHARGE,
      DEFAULT_GATEWAY_TRANSFER: process.env.DEFAULT_GATEWAY_TRANSFER,
      action: options?.action,
      forceType: options?.forceType,
      envFileExists: require('fs').existsSync('.env'),
      processEnvKeys: Object.keys(process.env).filter(key => key.includes('GATEWAY')).length
    });

    // If a specific type is forced, use it
    if (options?.forceType) {
      logger.info(`Using forced gateway type: ${options.forceType}`, {
        organizationId,
        action: options?.action,
        reason: 'Forced gateway type specified',
        FORCED_TYPE: options.forceType
      });
      return getProviderByType(options.forceType);
    }

    // Check if preferredType is being used somewhere
    if (options?.preferredType) {
      console.log('🚨🚨🚨 EXTREME DEBUG: preferredType found', options.preferredType);
      logger.info(`🔍 DEBUG: preferredType found: ${options.preferredType}`, {
        organizationId,
        preferredType: options.preferredType,
        action: options?.action
      });
    }

    // 🚀 NEW: Organization-specific gateway selection with fallback to environment variables
    let selectedGatewayType: string;

    logger.info(`🔍 DEBUG: About to select gateway type`, {
      organizationId,
      action: options?.action,
      timestamp: new Date().toISOString()
    });

    try {
      // First, try to get organization-specific gateway
      selectedGatewayType = await getOrganizationGatewayType(organizationId, options?.action);

      logger.info(`🚀 Using organization-specific gateway: ${selectedGatewayType}`, {
        organizationId,
        action: options?.action,
        gatewayType: selectedGatewayType,
        source: 'organization-specific',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      // Fallback to environment variables if no organization-specific gateway found
      logger.info(`No organization-specific gateway found, falling back to environment variables`, {
        organizationId,
        action: options?.action,
        error: error instanceof Error ? error.message : String(error)
      });

      if (options?.action === 'charge') {
        // PIX IN transactions (receiving money) - use environment variable or fallback to ZENDRY
        const envValue = process.env.DEFAULT_GATEWAY_CHARGE;
        const envValueUpper = envValue?.toUpperCase();
        selectedGatewayType = envValueUpper || 'ZENDRY';

        logger.info(`🚀 Using ${selectedGatewayType} for PIX IN (charge) - defined by ENV fallback`, {
          organizationId,
          action: options.action,
          gatewayType: selectedGatewayType,
          source: envValue ? 'environment' : 'fallback',
          reason: 'No organization-specific gateway configured',
          timestamp: new Date().toISOString()
        });
      } else if (options?.action === 'withdrawal') {
        // PIX OUT transactions (sending money) - use environment variable or fallback to TRANSFEERA
        const envValue = process.env.DEFAULT_GATEWAY_TRANSFER;
        const envValueUpper = envValue?.toUpperCase();
        selectedGatewayType = envValueUpper || 'TRANSFEERA';

        logger.info(`🚀 Using ${selectedGatewayType} for PIX OUT (withdrawal) - defined by ENV fallback`, {
          organizationId,
          action: options.action,
          gatewayType: selectedGatewayType,
          source: envValue ? 'environment' : 'fallback',
          reason: 'No organization-specific gateway configured',
          timestamp: new Date().toISOString()
        });
      } else if (options?.action === 'status') {
        // For status checks, use the same logic as charge (since most transactions are charges)
        const envValue = process.env.DEFAULT_GATEWAY_CHARGE;
        const envValueUpper = envValue?.toUpperCase();
        selectedGatewayType = envValueUpper || 'CARTWAVE';

        logger.info(`🚀 Using ${selectedGatewayType} for status check - defined by ENV fallback`, {
          organizationId,
          action: options.action,
          gatewayType: selectedGatewayType,
          source: envValue ? 'environment' : 'fallback',
          reason: 'No organization-specific gateway configured - using charge gateway for status',
          timestamp: new Date().toISOString()
        });
      } else {
        // For other actions or when action is not specified - use environment variable or fallback to PLUGGOU_PIX
        const envValue = process.env.DEFAULT_GATEWAY_CHARGE;
        const envValueUpper = envValue?.toUpperCase();
        selectedGatewayType = envValueUpper || 'PLUGGOU_PIX';

        logger.info(`🚀 Using ${selectedGatewayType} as default gateway - defined by ENV fallback`, {
          organizationId,
          action: options?.action,
          gatewayType: selectedGatewayType,
          source: envValue ? 'environment' : 'fallback',
          reason: 'No organization-specific gateway configured',
          timestamp: new Date().toISOString()
        });
      }
    }

    // Log final selection
    logger.info('Final gateway selection', { selectedGatewayType });

    logger.info(`🔍 DEBUG: Final gateway selection (after reload)`, {
      organizationId,
      selectedGatewayType,
      action: options?.action,
      willUseProvider: selectedGatewayType,
      providerExists: selectedGatewayType in ['ECOMOVI', 'MEDIUSPAG', 'TRANSFEERA', 'PLUGGOU_PIX', 'FLOW2PAY'],
      timestamp: new Date().toISOString(),
      ABOUT_TO_CALL_getProviderByType: true
    });

    // Return the provider directly for maximum performance
    logger.info(`🔍 DEBUG: About to call getProviderByType with: ${selectedGatewayType}`, {
      organizationId,
      selectedGatewayType,
      timestamp: new Date().toISOString()
    });

    const provider = getProviderByType(selectedGatewayType);

    logger.info('Provider returned', { providerType: provider.constructor.name });

    logger.info(`🔍 DEBUG: Provider returned (after reload)`, {
      organizationId,
      selectedGatewayType,
      providerType: provider.constructor.name,
      hasCreatePixPayment: typeof provider.createPixPayment === 'function',
      timestamp: new Date().toISOString(),
      SUCCESS: true
    });

    return provider;
  } catch (error) {
    logger.error("Error getting payment provider", { error, organizationId, options, timestamp: new Date().toISOString() });
    throw error;
  }
}

// Get provider by gateway type
export function getProviderByType(type: string): PaymentGatewayProvider {
  logger.info(`🔍 DEBUG: getProviderByType called with type: ${type}`, {
    type,
    typeUpper: type.toUpperCase()
  });

  let provider: PaymentGatewayProvider;

  switch (type.toUpperCase()) {
    case "REFLOWPAY":
      logger.info(`🔍 DEBUG: Returning REFLOWPAY provider`);
      provider = reflowpay;
      break;
    case "PRIMEPAG":
      logger.info(`🔍 DEBUG: Returning PRIMEPAG provider`);
      provider = primepag;
      break;
    case "PIXIUM":
      logger.info(`🔍 DEBUG: Returning PIXIUM provider`);
      provider = pixium;
      break;
    case "TRANSFEERA":
      // Transfeera pode ser usado para transferências e recebimentos
      logger.info(`🔍 DEBUG: Returning TRANSFEERA provider`);
      provider = transfeera;
      break;
    case "PLUGGOU_PIX":
      // Pluggou PIX API (integração com Flow2Pay)
      logger.info(`🔍 DEBUG: Returning PLUGGOU_PIX provider`);
      provider = pluggouPix;
      break;
    case "FLOW2PAY":
      // Direct Flow2Pay integration (eliminates intermediate layer)
      logger.info(`🔍 DEBUG: Returning FLOW2PAY provider`);
      provider = flow2pay;
      break;
    case "MEDIUSPAG":
      logger.info(`🔍 DEBUG: Returning MEDIUSPAG provider`);
      provider = mediuspag;
      break;
    case "ZENDRY":
      logger.info(`🔍 DEBUG: Returning ZENDRY provider`);
      provider = zendry;
      break;
    case "MOCKSIM":
      // Gateway de simulação para testes
      logger.info(`🔍 DEBUG: Returning MOCKSIM provider`);
      provider = mocksim;
      break;
    case "ECOMOVI":
      // Gateway Ecomovi/Onz Finance para PIX IN e PIX OUT
      logger.info(`🔍 DEBUG: Returning ECOMOVI provider`);
      provider = ecomovi;
      break;
    case "CARTWAVE":
      // Gateway Cartwave Hub para PIX IN e PIX OUT (cash in e cash out)
      logger.info(`🔍 DEBUG: Returning CARTWAVE provider`);
      provider = cartwave;
      break;
    case "PAGARME":
      logger.info(`🔍 DEBUG: Returning PAGARME provider`);
      provider = pagarme;
      break;
    case "OWEMPAY":
      // Gateway Owempay para PIX IN e PIX OUT
      logger.info(`🔍 DEBUG: Returning OWEMPAY provider`);
      provider = owempay;
      break;
    case "OWEMPAY_V2":
      // Gateway Owempay v2 para PIX IN (nova API)
      logger.info(`🔍 DEBUG: Returning OWEMPAY_V2 provider`);
      provider = owempayV2;
      break;
    case "ZEITBANK":
      // Gateway ZeitBank para PIX IN e PIX OUT
      logger.info(`🔍 DEBUG: Returning ZEITBANK provider`);
      provider = zeitbank;
      break;
    case "XDPAG":
      // Gateway XDPAG para PIX IN e PIX OUT
      logger.info(`🔍 DEBUG: Returning XDPAG provider`);
      provider = xdpag;
      break;
    // Add more gateway implementations as they become available
    default:
      logger.error(`🔍 DEBUG: Unsupported payment gateway type: ${type}`);
      throw new Error(`Unsupported payment gateway type: ${type}`);
  }

  // Provider type identification is now optional - we'll use constructor.name instead
  // This avoids any issues with frozen objects or extensibility
  logger.info("Provider created successfully", {
    type: type.toUpperCase(),
    constructorName: provider.constructor.name
  });

  return provider;
}

// Helper to get organization-specific gateway type based on action and priority
export async function getOrganizationGatewayType(
  organizationId: string,
  action?: string
): Promise<string> {
  logger.info(`Getting organization-specific gateway for action: ${action}`, {
    organizationId,
    action
  });

  try {
    // Determine the capability we need based on action
    let whereClause: any = {
      organizationId: organizationId,
      isActive: true,
      payment_gateway: {
        isActive: true
      }
    };

    // Filter by gateway capabilities based on action
    if (action === 'charge') {
      // For PIX IN (charge), we need gateways that can receive
      whereClause.payment_gateway.canReceive = true;
    } else if (action === 'withdrawal') {
      // For PIX OUT (withdrawal), we need gateways that can send
      whereClause.payment_gateway.canSend = true;
    }
    // For other actions, we don't filter by capabilities

    // Get organization gateways ordered by priority (lowest number = highest priority)
    const orgGateways = await db.organization_gateway.findMany({
      where: whereClause,
      include: {
        payment_gateway: {
          select: {
            id: true,
            name: true,
            type: true,
            instanceNumber: true,
            canReceive: true,
            canSend: true
          }
        }
      },
      orderBy: [
        { priority: 'asc' }, // Lower priority number = higher priority
        { createdAt: 'desc' }
      ],
    });

    logger.info(`Found ${orgGateways.length} organization gateways for action: ${action}`, {
      organizationId,
      action,
      gatewayCount: orgGateways.length,
      gateways: orgGateways.map(og => ({
        type: og.payment_gateway.type,
        priority: og.priority,
        instanceNumber: og.payment_gateway.instanceNumber,
        canReceive: og.payment_gateway.canReceive,
        canSend: og.payment_gateway.canSend
      }))
    });

    if (orgGateways.length === 0) {
      throw new Error(`No active organization-specific gateways found for action: ${action}`);
    }

    // Return the highest priority gateway type
    const selectedGateway = orgGateways[0];
    const gatewayType = selectedGateway.payment_gateway.type;

    logger.info(`Selected organization gateway: ${gatewayType}`, {
      organizationId,
      action,
      gatewayType,
      priority: selectedGateway.priority,
      instanceNumber: selectedGateway.payment_gateway.instanceNumber,
      gatewayId: selectedGateway.payment_gateway.id
    });

    return gatewayType;
  } catch (error) {
    logger.error(`Error getting organization gateway type`, {
      error: error instanceof Error ? error.message : String(error),
      organizationId,
      action
    });
    throw error;
  }
}

// Helper to get credentials for a gateway
export async function getGatewayCredentials(
  organizationId: string,
  type: string
): Promise<Record<string, any>> {
  const cacheKey = `${organizationId}:${type.toUpperCase()}`;

  // Check cache first
  const cached = credentialsCache.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
    logger.info(`Using cached credentials for gateway type ${type} for organization ${organizationId}`);
    return cached.credentials;
  }

  logger.info(`Getting credentials for gateway type ${type} for organization ${organizationId}`);

  try {
    // 🚀 PRIORITY 1: Try to find organization-specific gateway credentials first
    const orgGateway = await db.organization_gateway.findFirst({
      where: {
        organizationId: organizationId,
        isActive: true,
        payment_gateway: {
          type: {
            equals: type.toUpperCase(),
            mode: 'insensitive'
          },
          isActive: true
        }
      },
      include: {
        payment_gateway: {
          select: {
            id: true,
            name: true,
            type: true,
            instanceNumber: true,
            credentials: true,
            isActive: true,
            isGlobal: true,
            priority: true
          }
        }
      },
      orderBy: [
        { priority: 'asc' }, // Lower priority number = higher priority
        { createdAt: 'desc' },
      ],
    });

    if (orgGateway) {
      logger.info(`Found organization-specific gateway: ${orgGateway.payment_gateway.id}`);

      // Check if the relationship has its own credentials
      if ((orgGateway as any).credentials && Object.keys((orgGateway as any).credentials as Record<string, any>).length > 0) {
        logger.info(`✅ Using credentials from organization-gateway relationship (PRIORITY 1)`, {
          organizationId,
          gatewayType: type,
          gatewayId: orgGateway.payment_gateway.id,
          source: 'organization-gateway-relationship'
        });
        const creds = (orgGateway as any).credentials as Record<string, any>;

        // Cache the credentials
        credentialsCache.set(cacheKey, {
          credentials: creds,
          timestamp: Date.now()
        });

        return creds;
      }

      // If no credentials in the relationship, use the gateway's credentials
      if (orgGateway.payment_gateway.credentials && Object.keys(orgGateway.payment_gateway.credentials as Record<string, any>).length > 0) {
        logger.info(`✅ Using credentials from organization-specific gateway (PRIORITY 1)`, {
          organizationId,
          gatewayType: type,
          gatewayId: orgGateway.payment_gateway.id,
          source: 'organization-specific-gateway'
        });
        const creds = orgGateway.payment_gateway.credentials as Record<string, any>;

        // Cache the credentials
        credentialsCache.set(cacheKey, {
          credentials: creds,
          timestamp: Date.now()
        });

        return creds;
      }

      logger.info(`Organization-specific gateway found but no credentials available, trying global gateway`);
    }

    // 🚀 PRIORITY 2: Try to find a global gateway with credentials
    logger.info(`No organization-specific ${type} gateway found or no credentials, looking for global gateway`);

    const gateway = await db.payment_gateway.findFirst({
      where: {
        type: {
          equals: type.toUpperCase(),
          mode: 'insensitive'
        },
        isActive: true,
        isGlobal: true
      },
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' },
      ],
    });

    if (gateway && gateway.credentials && Object.keys(gateway.credentials as Record<string, any>).length > 0) {
      logger.info(`✅ Using credentials from global gateway (PRIORITY 2)`, {
        organizationId,
        gatewayType: type,
        gatewayId: gateway.id,
        source: 'global-gateway'
      });
      const creds = gateway.credentials as Record<string, any>;

      // Cache the credentials
      credentialsCache.set(cacheKey, {
        credentials: creds,
        timestamp: Date.now()
      });

      return creds;
    }

    // 🚀 PRIORITY 3: Fallback to environment credentials only if no database credentials found
    logger.info(`No database credentials found, falling back to environment variables (PRIORITY 3)`, {
      organizationId,
      gatewayType: type,
      source: 'environment-fallback'
    });

    const envCredentials = getEnvironmentCredentials(type.toUpperCase());
    if (envCredentials && Object.keys(envCredentials).length > 0) {
      // Verify that we have at least one valid credential
      const hasValidCredentials = envCredentials.apiKey || envCredentials.clientId || envCredentials.clientSecret || envCredentials.pixKey || envCredentials.email || envCredentials.password;

      if (hasValidCredentials) {
        logger.info(`✅ Using environment credentials as fallback (PRIORITY 3)`, {
          organizationId,
          gatewayType: type,
          source: 'environment-fallback',
          availableKeys: Object.keys(envCredentials)
        });

        // Cache the credentials
        credentialsCache.set(cacheKey, {
          credentials: envCredentials,
          timestamp: Date.now()
        });

        return envCredentials;
      }
    }

    // If we reach here, no credentials were found anywhere
    logger.error(`❌ No credentials found for ${type} gateway`, {
      organizationId,
      gatewayType: type,
      checkedSources: ['organization-gateway', 'global-gateway', 'environment']
    });

    throw new Error(`No credentials found for ${type} gateway. Please configure credentials in the database or environment variables.`);

  } catch (error) {
    logger.error("Error getting gateway credentials", {
      error: error instanceof Error ? error.message : String(error),
      organizationId,
      type
    });
    throw error;
  }
}

// Function to process a transaction with the appropriate gateway
export async function processTransaction(params: {
  transactionId: string;
  organizationId: string;
  gatewayType?: string;
  action: 'status' | 'refund' | 'withdrawal';
  additionalParams?: Record<string, any>;
}): Promise<any> {
  const { transactionId, organizationId, gatewayType, action, additionalParams = {} } = params;

  try {
    const provider = await getPaymentProvider(organizationId, {
      preferredType: gatewayType,
      action: action
    });

    // Transfeera agora pode ser usado para todas as operações
    // Removemos a restrição anterior

    switch (action) {
      case 'status':
        return provider.getTransactionStatus({
          transactionId,
          organizationId,
          transactionType: additionalParams.transactionType
        });
      case 'refund':
        return provider.processRefund({
          transactionId,
          organizationId,
          amount: additionalParams.amount,
          reason: additionalParams.reason
        });
      case 'withdrawal':
        return provider.processPixWithdrawal({
          amount: additionalParams.amount,
          pixKey: additionalParams.pixKey,
          pixKeyType: additionalParams.pixKeyType,
          postbackUrl: additionalParams.postbackUrl,
          organizationId,
          transactionId: additionalParams.transactionId,
        });
      default:
        throw new Error(`Unsupported action: ${action}`);
    }
  } catch (error) {
    logger.error(`Error processing ${action}`, { error, transactionId, organizationId });
    throw error;
  }
}
