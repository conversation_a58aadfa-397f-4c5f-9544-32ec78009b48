import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { reflowpay, retryWebhookDelivery } from "@repo/payments";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../middleware/auth";
import { apiKeyAuthMiddleware } from "../../middleware/apiKeyAuth";
import { ensureUserIsPartOfOrganization } from "../../utils/organization";
import { createWebhook, listWebhooks, updateWebhook, deleteWebhook } from "../../webhooks";
import { pixWebhooksRouter } from "./pix/router";
import { svixRouter } from "./svix/router";

// Combined authentication middleware that supports both session and API key auth
const combinedAuthMiddleware = async (c: any, next: () => Promise<void>) => {
  const apiKey = c.req.header("X-API-Key");

  if (apiKey) {
    // Use API key authentication
    return await apiKeyAuthMiddleware(c, next);
  } else {
    // Use session authentication
    return await authMiddleware(c, next);
  }
};

export const webhooksRouter = new Hono()
  // Não definimos basePath aqui, pois ele é definido no app.ts como "/webhooks"
  .basePath("");

// Mount PIX webhooks router
webhooksRouter.route("/pix", pixWebhooksRouter);

// Mount SVIX webhooks router
webhooksRouter.route("/svix", svixRouter);

// ReflowPay webhook handler
webhooksRouter.post(
  "/reflowpay",
  describeRoute({
    tags: ["Webhooks"],
    summary: "ReflowPay webhook handler",
    description: "Handles webhooks from ReflowPay",
    responses: {
      200: {
        description: "Webhook processed successfully",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      // Pass the request to the ReflowPay webhook handler
      return await reflowpay.webhookHandler(c.req);
    } catch (error) {
      logger.error("Error processing ReflowPay webhook", { error });
      return c.json({ error: "Failed to process webhook" }, 500);
    }
  }
);

// MEDIUSPAG webhook handler (PIX IN)
webhooksRouter.post(
  "/mediuspag",
  describeRoute({
    tags: ["Webhooks"],
    summary: "MEDIUSPAG webhook handler",
    description: "Handles webhooks from MEDIUSPAG for PIX IN transactions (charge/receiving)",
    responses: {
      200: {
        description: "Webhook processed successfully",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      logger.info("MEDIUSPAG webhook received via API router", {
        headers: Object.fromEntries(c.req.raw.headers),
        url: c.req.url
      });

      // Forward to the Next.js webhook handler
      const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/webhooks/mediuspag`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...Object.fromEntries(c.req.raw.headers)
        },
        body: await c.req.raw.text()
      });

      const result = await response.json();
      return c.json(result, response.status);
    } catch (error) {
      logger.error("Error processing MEDIUSPAG webhook", { error });
      return c.json({ error: "Failed to process webhook" }, 500);
    }
  }
);

// OWEMPAY V2 webhook handler (PIX IN)
webhooksRouter.post(
  "/owempay-v2",
  describeRoute({
    tags: ["Webhooks"],
    summary: "Owempay v2 webhook handler",
    description: "Handles webhooks from Owempay v2 for PIX IN transactions (charge/receiving)",
    responses: {
      200: {
        description: "Webhook processed successfully",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      logger.info("Owempay v2 webhook received via API router", {
        headers: Object.fromEntries(c.req.raw.headers),
        url: c.req.url
      });

      // Forward to the Next.js webhook handler
      const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/webhooks/owempay-v2`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...Object.fromEntries(c.req.raw.headers)
        },
        body: await c.req.raw.text()
      });

      const result = await response.json();
      return c.json(result, response.status);
    } catch (error) {
      logger.error("Error processing Owempay v2 webhook", { error });
      return c.json({ error: "Failed to process webhook" }, 500);
    }
  }
);

// XDPAG webhook handler (PIX IN and PIX OUT)
webhooksRouter.post(
  "/xdpag",
  describeRoute({
    tags: ["Webhooks"],
    summary: "XDPAG webhook handler",
    description: "Handles webhooks from XDPAG for PIX IN and PIX OUT transactions",
    responses: {
      200: {
        description: "Webhook processed successfully",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      logger.info("XDPAG webhook received via API router", {
        headers: Object.fromEntries(c.req.raw.headers),
        url: c.req.url
      });

      // Forward to the Next.js webhook handler
      const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/webhooks/xdpag`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...Object.fromEntries(c.req.raw.headers)
        },
        body: await c.req.raw.text()
      });

      const result = await response.json();
      return c.json(result, response.status);
    } catch (error) {
      logger.error("Error processing XDPAG webhook", { error });
      return c.json({ error: "Failed to process webhook" }, 500);
    }
  }
);

// TRANSFEERA webhook handler (PIX OUT)
webhooksRouter.post(
  "/transfeera",
  describeRoute({
    tags: ["Webhooks"],
    summary: "TRANSFEERA webhook handler",
    description: "Handles webhooks from TRANSFEERA for PIX OUT transactions (withdrawal/sending)",
    responses: {
      200: {
        description: "Webhook processed successfully",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      logger.info("TRANSFEERA webhook received via API router", {
        headers: Object.fromEntries(c.req.raw.headers),
        url: c.req.url
      });

      // Forward to the Next.js webhook handler
      const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/webhooks/transfeera`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...Object.fromEntries(c.req.raw.headers)
        },
        body: await c.req.raw.text()
      });

      const result = await response.json();
      return c.json(result, response.status);
    } catch (error) {
      logger.error("Error processing TRANSFEERA webhook", { error });
      return c.json({ error: "Failed to process webhook" }, 500);
    }
  }
);

// List webhooks for an organization
webhooksRouter.get(
  "/",
  validator("query", z.object({
    organizationId: z.string(),
  })),
  combinedAuthMiddleware,
  describeRoute({
    tags: ["Webhooks API"],
    summary: "List webhooks",
    description: "Lists all webhooks configured for an organization in the Pluggou webhook system. Supports both session and API key authentication.",
    responses: {
      200: {
        description: "Webhooks retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { organizationId } = c.req.valid("query");

      // For API key auth, validate that the API key belongs to the requested organization
      const apiKey = c.req.header("X-API-Key");
      if (apiKey) {
        const organization = c.get("organization");
        if (organization.id !== organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this organization" });
        }
      } else {
        // For session auth, check user organization membership
        const user = c.get("user");
        await ensureUserIsPartOfOrganization(user.id, organizationId);
      }

      // Use the webhooks service to get webhooks
      const result = await listWebhooks(organizationId);

      return c.json(result);
    } catch (error) {
      logger.error("Error listing webhooks", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list webhooks" });
    }
  })
);

// Create a new webhook
webhooksRouter.post(
  "/",
  validator("json", z.object({
    url: z.string().url(),
    events: z.array(z.string()),
    organizationId: z.string(),
    isActive: z.boolean().default(true),
  })),
  combinedAuthMiddleware,
  describeRoute({
    tags: ["Webhooks API"],
    summary: "Create a new webhook",
    description: "Creates a new webhook for an organization in the Pluggou webhook system for reliable event delivery. Supports both session and API key authentication.",
    responses: {
      201: {
        description: "Webhook created successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const {
        url,
        events,
        organizationId,
        isActive
      } = c.req.valid("json");

      // For API key auth, validate that the API key belongs to the requested organization
      const apiKey = c.req.header("X-API-Key");
      if (apiKey) {
        const organization = c.get("organization");
        if (organization.id !== organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this organization" });
        }
      } else {
        // For session auth, check user organization membership
        const user = c.get("user");
        await ensureUserIsPartOfOrganization(user.id, organizationId);
      }

      // Use the webhooks service to create webhook with Pluggou webhook system
      const result = await createWebhook({
        url,
        events,
        organizationId,
        isActive,
        useSvix: true, // Always use reliable delivery system for API-created webhooks
      });

      return c.json(result, 201);
    } catch (error) {
      logger.error("Error creating webhook", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      // Handle specific error messages from the webhook service
      if (error instanceof Error) {
        if (error.message === "A webhook with this URL already exists for this organization") {
          throw new HTTPException(400, { message: error.message });
        }
        if (error.message === "Organization not found") {
          throw new HTTPException(404, { message: error.message });
        }
      }

      throw new HTTPException(500, { message: "Failed to create webhook" });
    }
  })
);

// Update a webhook
webhooksRouter.patch(
  "/:id",
  validator("param", z.object({
    id: z.string(),
  })),
  validator("json", z.object({
    url: z.string().url().optional(),
    events: z.array(z.string()).optional(),
    isActive: z.boolean().optional(),
    regenerateSecret: z.boolean().optional(),
  })),
  combinedAuthMiddleware,
  describeRoute({
    tags: ["Webhooks API"],
    summary: "Update a webhook",
    description: "Updates an existing webhook configuration in the Pluggou webhook system. Supports both session and API key authentication.",
    responses: {
      200: {
        description: "Webhook updated successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Webhook not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { id } = c.req.valid("param");
      const {
        url,
        events,
        isActive,
        regenerateSecret
      } = c.req.valid("json");

      // Get the webhook first to check organization ownership
      const webhook = await db.webhook.findUnique({
        where: { id },
      });

      if (!webhook) {
        throw new HTTPException(404, { message: "Webhook not found" });
      }

      // For API key auth, validate that the API key belongs to the webhook's organization
      const apiKey = c.req.header("X-API-Key");
      if (apiKey) {
        const organization = c.get("organization");
        if (organization.id !== webhook.organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this webhook" });
        }
      } else {
        // For session auth, check user organization membership
        const user = c.get("user");
        await ensureUserIsPartOfOrganization(user.id, webhook.organizationId);
      }

      // Use the webhooks service to update webhook with Pluggou webhook system
      const result = await updateWebhook(id, {
        url,
        events,
        isActive,
        regenerateSecret,
        useSvix: true, // Always use reliable delivery system for API-updated webhooks
      });

      return c.json(result);
    } catch (error) {
      logger.error("Error updating webhook", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      // Handle specific error messages from the webhook service
      if (error instanceof Error) {
        if (error.message === "Webhook not found") {
          throw new HTTPException(404, { message: error.message });
        }
        if (error.message === "A webhook with this URL already exists for this organization") {
          throw new HTTPException(400, { message: error.message });
        }
      }

      throw new HTTPException(500, { message: "Failed to update webhook" });
    }
  })
);

// Delete a webhook
webhooksRouter.delete(
  "/:id",
  validator("param", z.object({
    id: z.string(),
  })),
  combinedAuthMiddleware,
  describeRoute({
    tags: ["Webhooks API"],
    summary: "Delete a webhook",
    description: "Removes a webhook from the Pluggou webhook system with atomic cleanup. Supports both session and API key authentication.",
    responses: {
      200: {
        description: "Webhook deleted successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Webhook not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { id } = c.req.valid("param");

      // Get the webhook first to check organization ownership
      const webhook = await db.webhook.findUnique({
        where: { id },
      });

      if (!webhook) {
        throw new HTTPException(404, { message: "Webhook not found" });
      }

      // For API key auth, validate that the API key belongs to the webhook's organization
      const apiKey = c.req.header("X-API-Key");
      if (apiKey) {
        const organization = c.get("organization");
        if (organization.id !== webhook.organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this webhook" });
        }
      } else {
        // For session auth, check user organization membership
        const user = c.get("user");
        await ensureUserIsPartOfOrganization(user.id, webhook.organizationId);
      }

      // Use the main deleteWebhook function which handles atomic cleanup
      const result = await deleteWebhook(id);

      return c.json(result);
    } catch (error) {
      logger.error("Error deleting webhook", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      // Handle specific error messages
      if (error instanceof Error && error.message === "Webhook not found") {
        throw new HTTPException(404, { message: "Webhook not found" });
      }

      throw new HTTPException(500, { message: "Failed to delete webhook" });
    }
  })
);

// Get webhook events for an organization
webhooksRouter.get(
  "/events",
  validator("query", z.object({
    organizationId: z.string(),
    limit: z.string().optional().default("10").transform(Number),
    offset: z.string().optional().default("0").transform(Number),
    type: z.string().optional(),
  })),
  combinedAuthMiddleware,
  describeRoute({
    tags: ["Webhooks API"],
    summary: "List webhook events",
    description: "Lists webhook events for an organization",
    responses: {
      200: {
        description: "Webhook events retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { organizationId, limit, offset, type } = c.req.valid("query");

      // For API key auth, validate that the API key belongs to the requested organization
      const apiKey = c.req.header("X-API-Key");
      if (apiKey) {
        const organization = c.get("organization");
        if (organization.id !== organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this organization" });
        }
      } else {
        // For session auth, check user organization membership
        const user = c.get("user");
        await ensureUserIsPartOfOrganization(user.id, organizationId);
      }

      // Build the where clause
      const where: any = { organizationId };
      if (type) {
        where.type = type;
      }

      // Get the total count
      const total = await db.webhook_event.count({ where });

      // Get the events
      const events = await db.webhook_event.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        take: limit,
        skip: offset,
        include: {
          transaction: {
            select: {
              id: true,
              externalId: true,
              referenceCode: true,
              status: true,
              type: true,
              amount: true,
              createdAt: true,
              paymentAt: true,
            },
          },
        },
      });

      return c.json({
        data: events,
        meta: {
          total,
          limit,
          offset,
        },
      });
    } catch (error) {
      logger.error("Error listing webhook events", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list webhook events" });
    }
  })
);

// Get webhook deliveries for an event
webhooksRouter.get(
  "/events/:eventId/deliveries",
  validator("param", z.object({
    eventId: z.string(),
  })),
  combinedAuthMiddleware,
  describeRoute({
    tags: ["Webhooks API"],
    summary: "List webhook deliveries for an event",
    description: "Lists webhook deliveries for a specific event",
    responses: {
      200: {
        description: "Webhook deliveries retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Event not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { eventId } = c.req.valid("param");

      // Check if the event exists
      const event = await db.webhook_event.findUnique({
        where: { id: eventId },
      });

      if (!event) {
        throw new HTTPException(404, { message: "Webhook event not found" });
      }

      // For API key auth, validate that the API key belongs to the event's organization
      const apiKey = c.req.header("X-API-Key");
      if (apiKey) {
        const organization = c.get("organization");
        if (organization.id !== event.organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this webhook event" });
        }
      } else {
        // For session auth, check user organization membership
        const user = c.get("user");
        await ensureUserIsPartOfOrganization(user.id, event.organizationId);
      }

      // Get the deliveries
      const deliveries = await db.webhook_delivery.findMany({
        where: {
          eventId,
        },
        include: {
          webhook: {
            select: {
              id: true,
              url: true,
              events: true,
              isActive: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      return c.json({
        data: deliveries,
      });
    } catch (error) {
      logger.error("Error listing webhook deliveries", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list webhook deliveries" });
    }
  })
);

// Retry a webhook delivery
webhooksRouter.post(
  "/deliveries/:id/retry",
  validator("param", z.object({
    id: z.string(),
  })),
  combinedAuthMiddleware,
  describeRoute({
    tags: ["Webhooks API"],
    summary: "Retry a webhook delivery",
    description: "Retries a failed webhook delivery",
    responses: {
      200: {
        description: "Webhook delivery retry scheduled",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Delivery not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  resolver(async (c) => {
    try {
      const { id } = c.req.valid("param");

      // Get the delivery
      const delivery = await db.webhook_delivery.findUnique({
        where: { id },
        include: {
          webhook: {
            select: {
              organizationId: true,
            },
          },
        },
      });

      if (!delivery) {
        throw new HTTPException(404, { message: "Webhook delivery not found" });
      }

      // For API key auth, validate that the API key belongs to the webhook's organization
      const apiKey = c.req.header("X-API-Key");
      if (apiKey) {
        const organization = c.get("organization");
        if (organization.id !== delivery.webhook.organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this webhook delivery" });
        }
      } else {
        // For session auth, check user organization membership
        const user = c.get("user");
        await ensureUserIsPartOfOrganization(user.id, delivery.webhook.organizationId);
      }

      // Retry the delivery
      const result = await retryWebhookDelivery(id);

      return c.json(result);
    } catch (error) {
      logger.error("Error retrying webhook delivery", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to retry webhook delivery" });
    }
  })
);


