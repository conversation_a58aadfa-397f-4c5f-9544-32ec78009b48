import { getOrganizationMembership } from "@repo/auth";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { getPaymentProvider } from "@repo/payments/provider/factory";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { z } from "zod";
import { authMiddleware } from "../../../middleware/auth";
// Imports removidos para evitar erros - usando funções diretas
// import { processGatewayPayment } from "./async-processor"; // Comment out or remove if no longer needed for other paths

// Cache simples em memória para otimização
const memoryCache = new Map<string, { data: any; timestamp: number }>();

// Cache TTLs (em segundos) - cache em memória
const CACHE_TTL = {
  GATEWAY: 30 * 60, // 30 minutos - gateways mudam raramente
  CREDENTIALS: 60 * 60, // 1 hora - credenciais mudam raramente
  ORGANIZATION: 15 * 60, // 15 minutos - organizações podem mudar
  MEMBERSHIP: 15 * 60, // 15 minutos - memberships podem mudar
  ORGANIZATION_BY_API_KEY: 60 * 60, // 1 hora - API keys são estáveis
  PAYMENT_PROVIDER: 30 * 60, // 30 minutos - providers são estáveis
} as const;

function getCacheKey(type: string, ...args: string[]) {
  return `${type}:${args.join(':')}`;
}

function getFromCache(key: string, ttl: number) {
  const cached = memoryCache.get(key);
  const ttlMs = ttl * 1000; // Convert seconds to milliseconds for memory cache
  if (cached && Date.now() - cached.timestamp < ttlMs) {
    logger.info(`Cache HIT for key: ${key}`);
    return cached.data;
  }
  logger.info(`Cache MISS for key: ${key}`);
  return null;
}

function setCache(key: string, data: any, ttl: number) {
  const ttlMs = ttl * 1000; // Convert seconds to milliseconds for memory cache
  memoryCache.set(key, { data, timestamp: Date.now() });
  logger.info(`Cache SET for key: ${key}, TTL: ${ttlMs}ms`);
  // Limpar cache antigo periodicamente
  setTimeout(() => {
    const cached = memoryCache.get(key);
    if (cached && Date.now() - cached.timestamp > ttlMs) {
      memoryCache.delete(key);
    }
  }, ttlMs);
}

// Cache simples em memória (sem Redis)
async function getCachedData(key: string, ttl: number) {
  return getFromCache(key, ttl);
}

async function setCachedData(key: string, data: any, ttl: number) {
  setCache(key, data, ttl);
}

// Cache para gateways com TTL mais agressivo
const gatewayCache = new Map<string, { gateway: any, timestamp: number }>();
const GATEWAY_CACHE_TTL = 30 * 60 * 1000; // 30 minutos (aumentado de 5 minutos)

// Definir o tipo para as variáveis de contexto corretamente conforme o middleware de autenticação
interface Variables {
  session: {
    id: string;
    userId: string;
    expiresAt: Date;
    token: string;
    createdAt: Date;
    updatedAt: Date;
    activeOrganizationId?: string;
    [key: string]: any;
  };
  user: {
    id: string;
    name?: string;
    email?: string;
    role?: string;
    [key: string]: any;
  };
  organization?: {
    id: string;
    name?: string;
    [key: string]: any;
  };
  apiKey?: {
    id: string;
    organizationId: string;
    [key: string]: any;
  };
}

// Schema para validar a criação de transações
const createTransactionSchema = z.object({
  amount: z.number().positive(),
  customerName: z.string().min(1),
  customerEmail: z.string().email(),
  customerPhone: z.string().optional(),
  customerDocument: z.string().optional(),
  customerDocumentType: z.enum(["cpf", "cnpj"]).optional(),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  organizationId: z.string(),
  // Gateway fields removed as requested
});

// Importante: Não usamos basePath pois já foi definido no paymentsRouter
export const transactionsRouter = new Hono<{ Variables: Variables }>();

// Create a new transaction (Pix payment)
transactionsRouter.post(
  "/",
  authMiddleware as any,
  validator("json", createTransactionSchema),
  describeRoute({
    tags: ["Transactions"],
    summary: "Create a new transaction (Pix payment)",
    description: "Creates a new Pix payment transaction",
    responses: {
      201: {
        description: "Transaction created successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    const timings = {
      start: Date.now(),
      dbLookupComplete: 0,
      gatewaySelectionComplete: 0,
      gatewayCallStart: 0,
      gatewayCallComplete: 0,
      dbInsertComplete: 0
    };
    const abortController = new AbortController();
    const timeoutId = setTimeout(() => {
      logger.warn("Hono request timeout triggered before completion", { path: c.req.path });
      abortController.abort();
    }, 25000); // Increased timeout to 25 seconds for Pluggou + DB

    try {
      // Verify database connection is available
      if (!db) {
        logger.error("Database connection is not available", {
          path: c.req.path,
          method: c.req.method
        });
        throw new HTTPException(500, { message: "Database connection unavailable" });
      }

      const session = c.get("session");
      const {
        amount,
        customerName,
        customerEmail,
        customerPhone,
        customerDocument,
        description,
        metadata: requestMetadata,
        organizationId,
      } = c.req.valid("json");

      // Check membership with simple memory cache
      const membershipCacheKey = getCacheKey('MEMBERSHIP', session.userId, organizationId);
      let membership = await getCachedData(membershipCacheKey, CACHE_TTL.ORGANIZATION);

      if (!membership) {
        membership = await getOrganizationMembership(session.userId, organizationId);
        if (membership) {
          // Cache for next requests
          await setCachedData(membershipCacheKey, membership, CACHE_TTL.ORGANIZATION);
        }
      }

      if (!membership) {
        clearTimeout(timeoutId);
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      const referenceCode = `tx_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      const idempotencyKey = `tx_${customerEmail}_${amount}_${Date.now().toString().substring(0, 8)}`; // Consider a more robust idempotency key

      // Enhanced duplicate checking - prevent transaction duplication
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000); // Extended to 5 minutes
      const existingTransaction = await db.transaction.findFirst({
        where: {
          OR: [
            // Exact reference code match (fastest)
            { referenceCode },
            // Recent duplicate by customer + amount + organization (with longer time limit)
            {
              customerEmail,
              amount,
              organizationId,
              type: "CHARGE",
              status: { in: ["PENDING", "PROCESSING", "APPROVED"] }, // Only active transactions
              createdAt: { gte: fiveMinutesAgo }
            }
          ]
        },
        select: {
          id: true,
          referenceCode: true,
          status: true,
          amount: true,
          customerEmail: true,
          customerName: true,
          createdAt: true,
          metadata: true
        },
        orderBy: [
          // Prioritize exact reference matches first
          { referenceCode: referenceCode ? 'desc' : 'asc' },
          // Then most recent
          { createdAt: "desc" }
        ]
      });
      timings.dbLookupComplete = Date.now();

      if (existingTransaction) {
        const existingMetadata = existingTransaction.metadata as Record<string, any> || {};
        if (existingMetadata.pixCode || existingMetadata.pixQrCode) {
          clearTimeout(timeoutId);
          logger.info("Duplicate transaction with PIX data found, returning existing.", { id: existingTransaction.id });
          return c.json({
            id: existingTransaction.id,
            referenceCode: existingTransaction.referenceCode,
            status: existingTransaction.status.toLowerCase(),
            externalId: existingMetadata.txid || existingMetadata.externalId,
            pix: {
              qrCode: {
                emv: existingMetadata.pixCode,
                imagem: existingMetadata.pixQrCode,
              },
              expirationDate: existingMetadata.pixExpiresAt,
              txid: existingMetadata.txid
            },
            paymentInfo: {
              amount: existingTransaction.amount,
              customerEmail: existingTransaction.customerEmail,
              customerName: existingTransaction.customerName,
              createdAt: existingTransaction.createdAt
            },
            message: "PIX charge created successfully",
          }, 200); // 200 OK for existing resource
        }
        // If duplicate exists but no PIX data, proceed to generate PIX for it
        logger.info("Duplicate transaction found without PIX data, proceeding to generate PIX for it.", { id: existingTransaction.id });
      }

              // 🚀 PERFORMANCE OPTIMIZATION: Skip gateway selection logic
      // Gateway is now selected directly in the provider factory based on environment variables
      // This eliminates the gatewaySelection time that was causing ~1.8s delay
      logger.info("Skipping gateway selection - using environment-based provider factory", {
        organizationId,
        reason: "Performance optimization - gateway selected in provider factory",
        eliminatedTime: "~1.8s database query time"
      });

      // Create virtual gateway info for metadata (no database query needed)
      const defaultGatewayType = process.env.DEFAULT_GATEWAY_CHARGE?.toUpperCase() || 'ZENDRY';
      const gateway = {
        id: `env_${defaultGatewayType.toLowerCase()}_${organizationId}`,
        type: defaultGatewayType,
        name: `${defaultGatewayType} (Environment Default)`
      };

      timings.gatewaySelectionComplete = Date.now();

      // Use dual gateway setup for all providers
      let provider;

      // Always use the dual gateway factory for consistent routing
      provider = await getPaymentProvider(organizationId, {
        action: 'charge', // This is a PIX IN transaction (charge) - will select based on ENV
        // Don't force type - let the dual gateway logic decide
      });

      // Get the provider type for logging - use constructor name only
      const providerType = provider.constructor.name || 'Unknown';

      logger.info("Attempting optimized synchronous PIX creation with provider", {
        selectedProvider: providerType,
        action: "charge",
        organizationId,
        dualGatewaySetup: true
      });
      timings.gatewayCallStart = Date.now();

      // Optimized synchronous flow with memory cache

      // Prepare parameters for the provider's createPixPayment method
      // O MediusPag provider já retorna os dados do PIX sincronamente

      const providerParams = {
        amount,
        customerName,
        customerEmail,
        customerPhone,
        customerDocument,
        description: description || `Cobrança PIX para ${customerName}`,
        organizationId,
        externalCode: existingTransaction?.referenceCode || referenceCode,
        idempotencyKey: idempotencyKey,
        metadata: {
          ...(requestMetadata || {}),
          source: "api-optimized-sync",
          gatewayId: null, // Não usar gatewayId para gateways baseados em ambiente
          gatewayName: gateway.name,
        },
      };

      logger.info("Calling provider.createPixPayment with params", {
        providerType,
        organizationId,
        amount,
        customerEmail,
        hasExternalCode: !!providerParams.externalCode,
        hasIdempotencyKey: !!providerParams.idempotencyKey,
        metadataKeys: Object.keys(providerParams.metadata)
      });

      const paymentResult = await provider.createPixPayment(providerParams);

      timings.gatewayCallComplete = Date.now();

      if (!paymentResult || !paymentResult.success) {
        clearTimeout(timeoutId);
        logger.error("Provider failed to create PIX payment", {
          error: paymentResult,
          organizationId,
          provider: providerType
        });
        throw new HTTPException(500, { message: "Failed to create PIX payment with provider" });
      }

      // O provider já criou a transação no banco de dados
      // Vamos buscar a transação criada
      let dbTransaction;

      // 🚨 DEBUG: Log do paymentResult para entender a estrutura
      logger.info("Payment result structure", {
        hasTransactionId: !!paymentResult.transactionId,
        transactionId: paymentResult.transactionId,
        hasExternalId: !!paymentResult.externalId,
        externalId: paymentResult.externalId,
        hasSuccess: !!paymentResult.success,
        success: paymentResult.success,
        paymentResultKeys: Object.keys(paymentResult),
        organizationId
      });

      if (paymentResult.transactionId) {
        // Buscar a transação criada pelo provider
        logger.info("Searching for transaction in database", {
          transactionId: paymentResult.transactionId,
          organizationId
        });

        dbTransaction = await db.transaction.findUnique({
          where: { id: paymentResult.transactionId }
        });

        if (!dbTransaction) {
          logger.error("Transaction created by provider not found in database", {
            transactionId: paymentResult.transactionId,
            organizationId,
            searchQuery: { id: paymentResult.transactionId }
          });
          throw new HTTPException(500, { message: "Transaction creation failed" });
        }

        logger.info("Using transaction created by provider", {
          transactionId: dbTransaction.id,
          externalId: dbTransaction.externalId,
          provider: providerType
        });
      } else if (existingTransaction) {
        // Atualizar transação existente se o provider não criou uma nova
        dbTransaction = await db.transaction.update({
          where: { id: existingTransaction.id },
          data: {
            externalId: paymentResult.externalId,
            metadata: {
              ...(existingTransaction.metadata as Record<string, any> || {}),
              ...paymentResult.metadata,
              pixCode: paymentResult.pixCode,
              pixQrCode: paymentResult.pixQrCode,
              pixExpiresAt: paymentResult.pixExpiresAt,
            }
          }
        });
      } else {
        // Fallback: criar transação se o provider não criou uma
        logger.warn("Provider didn't create transaction, creating fallback transaction", {
          organizationId,
          provider: providerType
        });

        dbTransaction = await db.transaction.create({
          data: {
            externalId: paymentResult.externalId,
            referenceCode: referenceCode,
            customerName,
            customerEmail,
            customerPhone: customerPhone || "",
            customerDocument: customerDocument || "",
            amount,
            status: "PENDING",
            type: "CHARGE",
            description: description || "Pagamento via Pix",
            metadata: {
              ...(requestMetadata || {}),
              pixCode: paymentResult.pixCode,
              pixQrCode: paymentResult.pixQrCode,
              pixExpiresAt: paymentResult.pixExpiresAt,
              ...paymentResult.metadata,
            },
            organizationId,
            gatewayId: null, // Não usar gatewayId para gateways baseados em ambiente
            gatewayName: gateway.name,
          }
        });
      }

      timings.dbInsertComplete = Date.now();

      // 🚀 PROCESSAR TAXAS IMEDIATAMENTE APÓS CRIAÇÃO DA TRANSAÇÃO
      // Processar taxas mesmo para transações PENDING para garantir que sejam salvas no banco
      try {
        logger.info("🚀 PROCESSING FEES - Starting fee processing for transaction", {
          transactionId: dbTransaction.id,
          organizationId,
          amount: dbTransaction.amount
        });

        const { processTransactionFees } = await import("@repo/payments/src/taxes/fee-service");
        logger.info("✅ Fee service imported successfully");

        const feeResult = await processTransactionFees(dbTransaction, 'CHARGE');
        logger.info("✅ Fee calculation completed", {
          success: feeResult.success,
          totalFee: feeResult.fees?.totalFee,
          netAmount: feeResult.netAmount
        });

        if (feeResult.success) {
          logger.info("💰 UPDATING TRANSACTION WITH FEES", {
            transactionId: dbTransaction.id,
            percentFee: feeResult.fees.percentFee,
            fixedFee: feeResult.fees.fixedFee,
            totalFee: feeResult.fees.totalFee,
            netAmount: feeResult.netAmount
          });

          // Atualizar a transação com as taxas calculadas
          await db.transaction.update({
            where: { id: dbTransaction.id },
            data: {
              percentFee: feeResult.fees.percentFee,
              fixedFee: feeResult.fees.fixedFee,
              totalFee: feeResult.fees.totalFee,
              netAmount: feeResult.netAmount,
              metadata: {
                ...(dbTransaction.metadata as Record<string, any> || {}),
                fees: {
                  percentFee: feeResult.fees.percentFee,
                  fixedFee: feeResult.fees.fixedFee,
                  totalFee: feeResult.fees.totalFee,
                  source: feeResult.fees.source || 'organization',
                  calculatedAt: new Date().toISOString()
                },
                netAmount: feeResult.netAmount,
                feeProcessed: true,
                feeProcessedAt: new Date().toISOString()
              }
            }
          });

          logger.info("✅ FEES SAVED SUCCESSFULLY", {
            transactionId: dbTransaction.id,
            percentFee: feeResult.fees.percentFee,
            fixedFee: feeResult.fees.fixedFee,
            totalFee: feeResult.fees.totalFee,
            netAmount: feeResult.netAmount
          });
        } else {
          logger.warn("❌ FEE PROCESSING FAILED", {
            transactionId: dbTransaction.id,
            reason: "Fee processing returned success: false",
            feeResult: feeResult
          });
        }
      } catch (feeError) {
        logger.error("❌ CRITICAL ERROR IN FEE PROCESSING", {
          error: feeError,
          errorMessage: feeError instanceof Error ? feeError.message : String(feeError),
          errorStack: feeError instanceof Error ? feeError.stack : undefined,
          transactionId: dbTransaction.id,
          organizationId,
          amount: dbTransaction.amount
        });
        // Don't fail the transaction creation if fee processing fails
      }

      logger.info("Optimized synchronous PIX creation successful", {
        transactionId: dbTransaction.id,
        externalId: paymentResult.externalId,
        hasPixCode: !!paymentResult.pixCode,
        hasPixQrCode: !!paymentResult.pixQrCode,
        organizationId,
        totalTime: Date.now() - timings.start
      });

      // 🚀 WEBHOOK EVENT TRIGGER FOR TRANSACTION CREATION
      // Trigger webhook events for the newly created transaction
      try {
        logger.info("Triggering webhook events for newly created transaction", {
          transactionId: dbTransaction.id,
          status: dbTransaction.status,
          organizationId
        });

        const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
        await triggerTransactionEvents(dbTransaction, undefined, false); // No previous status (new transaction)

        logger.info("Successfully triggered webhook events for transaction creation", {
          transactionId: dbTransaction.id,
          status: dbTransaction.status,
          organizationId
        });
      } catch (webhookError) {
        logger.error("Error triggering webhook events for transaction creation", {
          error: webhookError,
          transactionId: dbTransaction.id,
          organizationId
        });
        // Don't fail the transaction creation if webhook fails
      }

      clearTimeout(timeoutId);

      // 🚨 DEBUG: Log dos dados retornados pelo provider
      logger.info("Payment result data structure", {
        hasQrCode: !!paymentResult.qrCode,
        hasQrCodeImage: !!paymentResult.qrCodeImage,
        hasExpiration: !!paymentResult.expiration,
        hasPix: !!paymentResult.pix,
        paymentResultKeys: Object.keys(paymentResult),
        pixKeys: paymentResult.pix ? Object.keys(paymentResult.pix) : [],
        qrCodeLength: paymentResult.qrCode ? paymentResult.qrCode.length : 0,
        qrCodeImageLength: paymentResult.qrCodeImage ? paymentResult.qrCodeImage.length : 0,
        qrCodeValue: paymentResult.qrCode ? paymentResult.qrCode.substring(0, 50) + '...' : 'null',
        qrCodeImageValue: paymentResult.qrCodeImage ? paymentResult.qrCodeImage.substring(0, 50) + '...' : 'null'
      });

      // 🚨 DEBUG: Log da resposta final que será enviada
      const finalResponse = {
        id: dbTransaction.id,
        referenceCode: dbTransaction.referenceCode,
        status: dbTransaction.status.toLowerCase(),
        externalId: paymentResult.externalId,
        pix: {
          qrCode: {
            emv: paymentResult.pixCode || paymentResult.qrCode || paymentResult.pix?.payload || paymentResult.pix?.qrCode?.emv,
            imagem: paymentResult.pixQrCode || paymentResult.qrCodeImage || paymentResult.pix?.encodedImage || paymentResult.pix?.qrCode?.imagem,
          },
          expirationDate: paymentResult.pixExpiresAt || paymentResult.expiration || paymentResult.pix?.expirationDate,
          txid: paymentResult.externalId || paymentResult.pix?.txid
        },
        paymentInfo: {
          amount: dbTransaction.amount,
          customerEmail: dbTransaction.customerEmail,
          customerName: dbTransaction.customerName,
          createdAt: dbTransaction.createdAt
        },
        message: "PIX charge created successfully",
        timings: {
          total: Date.now() - timings.start,
          dbLookup: timings.dbLookupComplete - timings.start,
          gatewaySelection: timings.gatewaySelectionComplete - timings.start,
          gatewayCall: timings.gatewayCallComplete - timings.gatewayCallStart,
          dbInsert: timings.dbInsertComplete - timings.gatewayCallComplete
        }
      };

      logger.info("🚀 Final response structure", {
        hasPix: !!finalResponse.pix,
        hasQrCode: !!finalResponse.pix?.qrCode,
        hasEmv: !!finalResponse.pix?.qrCode?.emv,
        hasImagem: !!finalResponse.pix?.qrCode?.imagem,
        emvLength: finalResponse.pix?.qrCode?.emv ? finalResponse.pix.qrCode.emv.length : 0,
        imagemLength: finalResponse.pix?.qrCode?.imagem ? finalResponse.pix.qrCode.imagem.length : 0,
        emvValue: finalResponse.pix?.qrCode?.emv ? finalResponse.pix.qrCode.emv.substring(0, 50) + '...' : 'null',
        imagemValue: finalResponse.pix?.qrCode?.imagem ? finalResponse.pix.qrCode.imagem.substring(0, 50) + '...' : 'null'
      });

      // Resposta otimizada com dados completos do PIX
      return c.json(finalResponse, 201);

    } catch (error: any) {
      clearTimeout(timeoutId);
      logger.error("Error in POST /api/payments/transactions", {
        error: error.message,
        stack: error.stack,
        organizationId: c.req.valid("json")?.organizationId,
        timings,
      });

      // Ensure we always return a proper JSON response
      let errorMessage = "Internal server error creating transaction";
      let statusCode = 500;

      if (error instanceof HTTPException) {
        errorMessage = error.message;
        statusCode = error.status;
      } else if (error.message) {
        errorMessage = error.message;
        // Handle specific error types
        if (error.message.includes("Chave de API inválida") || error.message.includes("Invalid API key")) {
          errorMessage = "Erro ao processar a cobrança: Chave de API do gateway PIX inválida";
          statusCode = 400;
        } else if (error.message.includes("timeout")) {
          errorMessage = "Erro ao processar a cobrança: Timeout na comunicação com o gateway";
          statusCode = 408;
        }
      }

      // Always return structured JSON error
      return c.json({
        success: false,
        error: errorMessage,
        message: errorMessage,
        details: {
          timestamp: new Date().toISOString(),
          organizationId: c.req.valid("json")?.organizationId,
          errorType: error.constructor.name
        }
      }, statusCode as any);
    }
  }
);

// List transactions
transactionsRouter.get(
  "/",
  authMiddleware as any, // Cast to any to bypass type issues temporarily
  validator("query", z.object({
    organizationId: z.string(),
    status: z.enum(["PENDING", "APPROVED", "REJECTED", "CANCELED", "PROCESSING", "REFUNDED", "BLOCKED"]).optional(),
    type: z.enum(["CHARGE", "SEND", "RECEIVE"]).optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
    page: z.string().transform(Number).default("1"),
    limit: z.string().transform(Number).default("10"),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "List transactions",
    description: "Lists transactions with optional filters",
    responses: {
      200: {
        description: "Transactions retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const {
        organizationId,
        status,
        type,
        startDate,
        endDate,
        page,
        limit
      } = c.req.valid("query");

      // Check if using API key authentication
      const apiKey = c.get("apiKey");
      const organization = c.get("organization");

      // If using API key, verify the organization matches
      if (apiKey && organization) {
        if (organization.id !== organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this organization" });
        }
      } else {
        // Otherwise verify normal membership
        const membership = await getOrganizationMembership(session.userId, organizationId);
        if (!membership) {
          throw new HTTPException(403, { message: "You don't have access to this organization" });
        }
      }

      // Build the query
      const where: any = {
        organizationId,
      };

      if (status) {
        where.status = status;
      }

      if (type) {
        where.type = type;
      }

      if (startDate || endDate) {
        where.createdAt = {};

        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }

        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      // Get total count
      const total = await db.transaction.count({ where });

      // Get transactions
      const transactions = await db.transaction.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      return c.json({
        data: transactions.map(tx => {
          return {
            id: tx.id,
            externalId: tx.externalId,
            referenceCode: tx.referenceCode,
            customerName: tx.customerName,
            customerEmail: tx.customerEmail,
            amount: tx.amount,
            status: tx.status,
            type: tx.type,
            createdAt: tx.createdAt,
            paymentAt: tx.paymentAt,
          };
        }),
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
        },
      });
    } catch (error) {
      logger.error("Error listing transactions", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to list transactions" });
    }
  }
);

// Get transaction by ID
transactionsRouter.get(
  "/:id",
  authMiddleware as any, // Cast to any to bypass type issues temporarily
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Get transaction by ID",
    description: "Retrieves a transaction by its ID",
    responses: {
      200: {
        description: "Transaction retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transaction not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");

      const transaction = await db.transaction.findUnique({
        where: { id },
        include: {
          payment_gateway: true,
          balance_history: true,
          cautionary_block: true,
        },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transaction not found" });
      }

      // Check if using API key authentication
      const apiKey = c.get("apiKey");
      const organization = c.get("organization");

      // If using API key, verify the organization matches
      if (apiKey && organization) {
        if (organization.id !== transaction.organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this transaction" });
        }
      } else {
        // Otherwise verify normal membership
        const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
        if (!membership) {
          throw new HTTPException(403, { message: "You don't have access to this transaction" });
        }
      }

      // Se a transação está em PROCESSING, verificar se o PIX foi gerado
      if (transaction.status === "PROCESSING") {
        const metadata = transaction.metadata as Record<string, any> || {};

        // Se temos dados do PIX, atualizar status para PENDING
        if (metadata.pixCode || metadata.pixQrCode) {
          await db.transaction.update({
            where: { id },
            data: {
              status: "PENDING",
              metadata: {
                ...metadata,
                statusUpdated: new Date().toISOString()
              }
            }
          });

          // Atualizar o objeto transaction para a resposta
          transaction.status = "PENDING" as any;
          transaction.metadata = {
            ...metadata,
            statusUpdated: new Date().toISOString()
          } as any;
        }
      }

      // If the transaction is pending, check its status with the gateway
      if (transaction.status === "PENDING" && transaction.externalId) {
        try {
          // Get the payment provider
          const provider = await getPaymentProvider(transaction.organizationId);

          const statusResult = await provider.getTransactionStatus({
            transactionId: transaction.externalId,
            organizationId: transaction.organizationId,
          });

          // Map status to our internal status
          let mappedStatus = transaction.status;

          if (statusResult.mappedStatus) {
            // If the provider returns a mapped status, use it
            mappedStatus = statusResult.mappedStatus;
          } else if (statusResult.status) {
            // Otherwise, try to map it ourselves
            const statusMap: Record<string, string> = {
              "pending": "PENDING",
              "approved": "APPROVED",
              "confirmed": "APPROVED",
              "received": "APPROVED",
              "rejected": "REJECTED",
              "canceled": "CANCELED",
              "processing": "PROCESSING",
              "refunded": "REFUNDED",
            };

            mappedStatus = statusMap[statusResult.status.toLowerCase()] || statusResult.status.toUpperCase();
          }

          // Update the transaction if the status has changed
          if (mappedStatus !== transaction.status) {
            const previousStatus = transaction.status;

            const updatedTransaction = await db.transaction.update({
              where: { id },
              data: {
                status: mappedStatus as any,
                paymentAt: statusResult.status === "approved" ? new Date() : transaction.paymentAt,
              },
            });

            // Trigger webhook events for the status change
            try {
              const { triggerTransactionEvents } = await import("@repo/payments/src/webhooks/events");
              await triggerTransactionEvents(updatedTransaction, previousStatus);
              logger.info("Triggered webhook events for status update during GET", {
                transactionId: id,
                previousStatus,
                newStatus: mappedStatus
              });
            } catch (webhookError) {
              logger.error("Failed to trigger webhook events for status update during GET", {
                error: webhookError,
                transactionId: id,
                previousStatus,
                newStatus: mappedStatus
              });
              // Don't fail the GET request if webhook triggering fails
            }

            // Refresh the transaction data
            transaction.status = mappedStatus as any;
            if (statusResult.status === "approved") {
              transaction.paymentAt = new Date();
            }
          }
        } catch (error) {
          logger.error("Error checking transaction status", { error, transactionId: id });
          // Continue with the current transaction data
        }
      }

      const metadata = transaction.metadata as Record<string, any> || {};

      // Prepare response based on transaction status
      let response: any = {
        id: transaction.id,
        externalId: transaction.externalId,
        referenceCode: transaction.referenceCode,
        status: transaction.status.toLowerCase(),
        amount: transaction.amount,
        customerName: transaction.customerName,
        customerEmail: transaction.customerEmail,
        customerPhone: transaction.customerPhone,
        customerDocument: transaction.customerDocument,
        type: transaction.type,
        description: transaction.description,
        createdAt: transaction.createdAt.toISOString(),
        updatedAt: transaction.updatedAt.toISOString(),
        paymentAt: transaction.paymentAt?.toISOString(),
        gateway: transaction.payment_gateway ? {
          id: transaction.payment_gateway.id,
          name: transaction.payment_gateway.name,
          type: transaction.payment_gateway.type,
        } : null,
      };

      // Se a transação está em PROCESSING, incluir informações de processamento
      if (transaction.status === "PROCESSING") {
        response.processing = {
          started: metadata.processingStarted,
          estimatedCompletion: metadata.estimatedCompletion || new Date(Date.now() + 5000).toISOString(),
          message: "PIX sendo gerado...",
          canPoll: true
        };
      }

      // Se a transação está PENDING e tem dados do PIX, incluí-los
      if (transaction.status === "PENDING" && (metadata.pixCode || metadata.pixQrCode)) {
        response.pix = {
          qrCode: {
            emv: metadata.pixCode,
            imagem: metadata.pixQrCode,
          },
          expirationDate: metadata.pixExpiresAt,
          txid: transaction.externalId || transaction.id
        };
        response.message = "PIX charge created successfully";
      }

      // Se a transação foi aprovada
      if (transaction.status === "APPROVED") {
        response.message = "Payment received successfully";
        response.paymentInfo = {
          amount: transaction.amount,
          customerEmail: transaction.customerEmail,
          customerName: transaction.customerName,
          paymentAt: transaction.paymentAt?.toISOString()
        };
      }

      return c.json(response);
    } catch (error) {
      logger.error("Error getting transaction", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to get transaction" });
    }
  }
);

// Process a refund
// transactionsRouter.post(
//   "/:id/refund",
//   authMiddleware,
//   validator("param", z.object({
//     id: z.string(),
//   })),
//   validator("json", z.object({
//     amount: z.number().positive().optional(),
//     reason: z.string().optional(),
//   })),
//   describeRoute({
//     tags: ["Transactions"],
//     summary: "Process a refund",
//     description: "Processes a refund for a transaction",
//     responses: {
//       200: {
//         description: "Refund processed successfully",
//       },
//       400: {
//         description: "Invalid request data",
//       },
//       401: {
//         description: "Unauthorized",
//       },
//       403: {
//         description: "Forbidden",
//       },
//       404: {
//         description: "Transaction not found",
//       },
//       500: {
//         description: "Internal server error",
//       },
//     },
//   }),
//   resolver(async (c) => {
//     try {
//       const session = c.get("session");
//       const { id } = c.req.valid("param");
//       const { amount, reason } = c.req.valid("json");

//       const transaction = await db.transaction.findUnique({
//         where: { id },
//       });

//       if (!transaction) {
//         throw new HTTPException(404, { message: "Transaction not found" });
//       }

//       // Verify organization membership
//       const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
//       if (!membership) {
//         throw new HTTPException(403, { message: "You don't have access to this transaction" });
//       }

//       // Check if the transaction can be refunded
//       if (transaction.status !== "APPROVED") {
//         throw new HTTPException(400, { message: "Only approved transactions can be refunded" });
//       }

//       // Use the full amount if not specified
//       const refundAmount = amount || transaction.amount;

//       // Create the refund in the database
//       const refund = await db.refund.create({
//         data: {
//           amount: refundAmount,
//           reason,
//           status: "PENDING",
//           transactionId: transaction.id,
//         },
//       });

//       // Process the refund with the appropriate gateway
//       if (transaction.externalId) {
//         try {
//           // Get the payment provider
//           const provider = await getPaymentProvider(transaction.organizationId);

//           const refundResult = await provider.processRefund({
//             transactionId: transaction.externalId,
//             amount: refundAmount,
//             reason,
//             organizationId: transaction.organizationId,
//           });

//           // Update the refund with the external ID
//           await db.refund.update({
//             where: { id: refund.id },
//             data: {
//               externalId: refundResult.id,
//               status: "PROCESSING",
//             },
//           });

//           // Update the transaction status
//           await db.transaction.update({
//             where: { id: transaction.id },
//             data: {
//               status: "REFUNDED",
//             },
//           });

//           return c.json({
//             id: refund.id,
//             amount: refund.amount,
//             status: "PROCESSING",
//             transactionId: transaction.id,
//           });
//         } catch (error) {
//           logger.error("Error processing refund with gateway", { error, transactionId: id });

//           // Update the refund status to failed
//           await db.refund.update({
//             where: { id: refund.id },
//             data: {
//               status: "REJECTED",
//             },
//           });

//           throw new HTTPException(500, { message: "Failed to process refund with payment gateway" });
//         }
//       } else {
//         throw new HTTPException(400, { message: "Transaction has no external ID" });
//       }
//     } catch (error) {
//       logger.error("Error processing refund", { error });

//       if (error instanceof HTTPException) {
//         throw error;
//       }

//       throw new HTTPException(500, { message: "Failed to process refund" });
//     }
//   })
// );

// Create a cautionary block
transactionsRouter.post(
  "/:id/block",
  authMiddleware as any,
  validator("param", z.object({
    id: z.string(),
  })),
  validator("json", z.object({
    reason: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Create a cautionary block",
    description: "Creates a cautionary block for a transaction",
    responses: {
      200: {
        description: "Block created successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transaction not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");
      const { reason } = c.req.valid("json");

      const transaction = await db.transaction.findUnique({
        where: { id },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transaction not found" });
      }

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this transaction" });
      }

      // Check if the transaction already has an active block
      const existingBlock = await db.cautionary_block.findFirst({
        where: {
          transactionId: transaction.id,
          status: "ACTIVE",
        },
      });

      if (existingBlock) {
        throw new HTTPException(400, { message: "Transaction already has an active block" });
      }

      // Create the block
      const block = await db.cautionary_block.create({
        data: {
          id: `block_${transaction.id}_${Date.now()}`,
          reason,
          status: "ACTIVE",
          transactionId: transaction.id,
          updatedAt: new Date(),
        },
      });

      // Update the transaction status
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          status: "BLOCKED",
        },
      });

      return c.json({
        id: block.id,
        reason: block.reason,
        status: block.status,
        transactionId: transaction.id,
        createdAt: block.createdAt,
      });
    } catch (error) {
      logger.error("Error creating cautionary block", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to create cautionary block" });
    }
  }
);

// Release a cautionary block
transactionsRouter.post(
  "/:id/unblock",
  authMiddleware as any,
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Release a cautionary block",
    description: "Releases a cautionary block for a transaction",
    responses: {
      200: {
        description: "Block released successfully",
      },
      400: {
        description: "Invalid request data",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transaction or block not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");

      const transaction = await db.transaction.findUnique({
        where: { id },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transaction not found" });
      }

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this transaction" });
      }

      // Find the active block
      const block = await db.cautionary_block.findFirst({
        where: {
          transactionId: transaction.id,
          status: "ACTIVE",
        },
      });

      if (!block) {
        throw new HTTPException(404, { message: "No active block found for this transaction" });
      }

      // Release the block
      await db.cautionary_block.update({
        where: { id: block.id },
        data: {
          status: "RELEASED",
          releasedAt: new Date(),
        },
      });

      // Restore the transaction's previous status
      await db.transaction.update({
        where: { id: transaction.id },
        data: {
          status: "APPROVED", // Assuming it was approved before being blocked
        },
      });

      return c.json({
        id: block.id,
        status: "RELEASED",
        transactionId: transaction.id,
        releasedAt: new Date(),
      });
    } catch (error) {
      logger.error("Error releasing cautionary block", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to release cautionary block" });
    }
  }
);

// Export transactions as CSV
transactionsRouter.get(
  "/export",
  authMiddleware as any,
  validator("query", z.object({
    organizationId: z.string(),
    status: z.enum(["PENDING", "APPROVED", "REJECTED", "CANCELED", "PROCESSING", "REFUNDED", "BLOCKED"]).optional(),
    type: z.enum(["CHARGE", "SEND", "RECEIVE"]).optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Export transactions as CSV",
    description: "Exports transactions as CSV with optional filters",
    responses: {
      200: {
        description: "CSV file",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const {
        organizationId,
        status,
        type,
        startDate,
        endDate
      } = c.req.valid("query");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Build the query
      const where: any = {
        organizationId,
      };

      if (status) {
        where.status = status;
      }

      if (type) {
        where.type = type;
      }

      if (startDate || endDate) {
        where.createdAt = {};

        if (startDate) {
          where.createdAt.gte = new Date(startDate);
        }

        if (endDate) {
          where.createdAt.lte = new Date(endDate);
        }
      }

      // Get transactions
      const transactions = await db.transaction.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        include: {
          payment_gateway: {
            select: {
              name: true,
            },
          },
        },
      });

      // Create CSV content
      let csv = "ID,Reference,Customer,Email,Amount,Status,Type,Created At,Payment At,Gateway\n";

      transactions.forEach(tx => {
        const row = [
          tx.id,
          tx.referenceCode || tx.externalId || "",
          tx.customerName,
          tx.customerEmail,
          tx.amount.toFixed(2),
          tx.status,
          tx.type,
          tx.createdAt.toISOString(),
          tx.paymentAt ? tx.paymentAt.toISOString() : "",
          tx.payment_gateway ? tx.payment_gateway.name : "",
        ].map(field => `"${String(field).replace(/"/g, '""')}"`).join(",");

        csv += row + "\n";
      });

      // Set headers for CSV download
      c.header("Content-Type", "text/csv");
      c.header("Content-Disposition", `attachment; filename="transactions-${new Date().toISOString().split("T")[0]}.csv"`);

      return c.body(csv);
    } catch (error) {
      logger.error("Error exporting transactions", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to export transactions" });
    }
  }
);

// Sync transaction status with gateway
transactionsRouter.post(
  "/:id/sync",
  authMiddleware as any,
  validator("param", z.object({
    id: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Sync transaction status with gateway",
    description: "Manually syncs a transaction's status with the payment gateway",
    responses: {
      200: {
        description: "Transaction status synced successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transaction not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { id } = c.req.valid("param");

      // Find the transaction
      const transaction = await db.transaction.findUnique({
        where: { id },
        include: {
          payment_gateway: true,
        },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transaction not found" });
      }

      // Check if using API key authentication
      const apiKey = c.get("apiKey");
      const organization = c.get("organization");

      // If using API key, verify the organization matches
      if (apiKey && organization) {
        if (organization.id !== transaction.organizationId) {
          throw new HTTPException(403, { message: "API key does not have access to this transaction" });
        }
      } else {
        // Otherwise verify normal membership
        const membership = await getOrganizationMembership(session.userId, transaction.organizationId);
        if (!membership) {
          throw new HTTPException(403, { message: "You don't have access to this transaction" });
        }
      }

      // Get the payment provider that processed this transaction
      const provider = await getPaymentProvider(transaction.organizationId, {
        forceType: transaction.payment_gateway?.type,
        action: 'status'
      });

      if (!provider) {
        throw new HTTPException(400, { message: "Payment provider not found" });
      }

      // Check status with the gateway, passing transaction type for better handling
      const statusResult = await provider.getTransactionStatus({
        transactionId: transaction.externalId || transaction.id,
        organizationId: transaction.organizationId,
        transactionType: transaction.type as 'CHARGE' | 'SEND',
      });

      // Map the status
      let mappedStatus = transaction.status;
      if (statusResult.mappedStatus) {
        // If the provider returns a mapped status, use it directly
        mappedStatus = statusResult.mappedStatus;
      } else if (statusResult.status) {
        // Fallback mapping
        const statusMap: Record<string, string> = {
          "PENDING": "PENDING",
          "APPROVED": "APPROVED",
          "CONFIRMED": "APPROVED",
          "RECEIVED": "APPROVED",
          "REJECTED": "REJECTED",
          "CANCELED": "CANCELED",
          "PROCESSING": "PROCESSING",
          "REFUNDED": "REFUNDED",
        };
        mappedStatus = (statusMap[statusResult.status.toUpperCase()] || transaction.status) as any;
      }

      // Update the transaction if the status has changed
      let updatedTransaction = transaction;
      if (mappedStatus !== transaction.status) {
        const isCompleted = mappedStatus === "APPROVED" ||
                          mappedStatus === "REFUNDED";

        updatedTransaction = await db.transaction.update({
          where: { id },
          data: {
            status: mappedStatus,
            paymentAt: isCompleted ? new Date() : transaction.paymentAt,
          },
          include: {
            payment_gateway: true,
          },
        });
      }

      return c.json({
        success: true,
        transaction: {
          id: updatedTransaction.id,
          externalId: updatedTransaction.externalId,
          status: updatedTransaction.status,
          amount: updatedTransaction.amount,
          customerName: updatedTransaction.customerName,
          customerEmail: updatedTransaction.customerEmail,
          type: updatedTransaction.type,
          date: updatedTransaction.createdAt.toISOString(),
          paymentDate: updatedTransaction.paymentAt?.toISOString(),
          updatedAt: updatedTransaction.updatedAt.toISOString(),
          gateway: updatedTransaction.payment_gateway ? {
            name: updatedTransaction.payment_gateway.name,
            type: updatedTransaction.payment_gateway.type,
          } : null,
        },
        previousStatus: transaction.status,
        newStatus: updatedTransaction.status,
        message: mappedStatus !== transaction.status
          ? "Transaction status updated successfully"
          : "Transaction status is already up to date",
      });
    } catch (error) {
      logger.error("Error syncing transaction status", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to sync transaction status" });
    }
  }
);

// Get transactions summary
transactionsRouter.get(
  "/summary",
  authMiddleware as any,
  validator("query", z.object({
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Get transactions summary",
    description: "Get summary statistics for transactions",
    responses: {
      200: {
        description: "Transactions summary retrieved successfully",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { organizationId } = c.req.valid("query");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Get total transactions count
      const totalTransactions = await db.transaction.count({
        where: { organizationId }
      });

      // Get approved transactions count
      const approvedTransactions = await db.transaction.count({
        where: {
          organizationId,
          status: "APPROVED"
        }
      });

      // Get pending transactions count
      const pendingTransactions = await db.transaction.count({
        where: {
          organizationId,
          status: "PENDING"
        }
      });

      // Get total financial volume
      const transactions = await db.transaction.findMany({
        where: { organizationId },
        select: { amount: true }
      });

      const financialVolume = transactions.reduce((sum, tx) => sum + tx.amount, 0);
      const averageTicket = transactions.length > 0 ? financialVolume / transactions.length : 0;

      // Get 30 day old counts for growth calculation
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const oldTotalTransactions = await db.transaction.count({
        where: {
          organizationId,
          createdAt: { lt: thirtyDaysAgo }
        }
      });

      const oldApprovedTransactions = await db.transaction.count({
        where: {
          organizationId,
          status: "APPROVED",
          createdAt: { lt: thirtyDaysAgo }
        }
      });

      const oldPendingTransactions = await db.transaction.count({
        where: {
          organizationId,
          status: "PENDING",
          createdAt: { lt: thirtyDaysAgo }
        }
      });

      const oldTransactions = await db.transaction.findMany({
        where: {
          organizationId,
          createdAt: { lt: thirtyDaysAgo }
        },
        select: { amount: true }
      });

      const oldFinancialVolume = oldTransactions.reduce((sum, tx) => sum + tx.amount, 0);

      // Calculate growth percentages
      const calculateGrowth = (current: number, previous: number) => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };

      const totalTransactionsGrowth = calculateGrowth(totalTransactions, oldTotalTransactions);
      const approvedTransactionsGrowth = calculateGrowth(approvedTransactions, oldApprovedTransactions);
      const pendingTransactionsGrowth = calculateGrowth(pendingTransactions, oldPendingTransactions);
      const financialVolumeGrowth = calculateGrowth(financialVolume, oldFinancialVolume);

      // Calculate approval rate
      const approvalRate = totalTransactions > 0 ? (approvedTransactions / totalTransactions) * 100 : 0;

      return c.json({
        totalTransactions: {
          count: totalTransactions,
          growth: totalTransactionsGrowth,
        },
        approvedTransactions: {
          count: approvedTransactions,
          growth: approvedTransactionsGrowth,
          approvalRate,
        },
        pendingTransactions: {
          count: pendingTransactions,
          growth: pendingTransactionsGrowth,
        },
        financialVolume: {
          amount: financialVolume,
          growth: financialVolumeGrowth,
          averageTicket,
        },
      });
    } catch (error) {
      logger.error("Error getting transactions summary", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to get transactions summary" });
    }
  }
);

// Map external ID to transaction
transactionsRouter.post(
  "/map-external-id",
  authMiddleware as any,
  validator("json", z.object({
    transactionId: z.string(),
    externalId: z.string(),
    gatewayType: z.string(),
    organizationId: z.string(),
  })),
  describeRoute({
    tags: ["Transactions"],
    summary: "Map external ID to transaction",
    description: "Map an external ID from a payment gateway to an internal transaction ID",
    responses: {
      200: {
        description: "External ID mapped successfully",
      },
      400: {
        description: "Bad request",
      },
      401: {
        description: "Unauthorized",
      },
      403: {
        description: "Forbidden",
      },
      404: {
        description: "Transaction not found",
      },
      500: {
        description: "Internal server error",
      },
    },
  }),
  async (c) => {
    try {
      const session = c.get("session");
      const { transactionId, externalId, gatewayType, organizationId } = c.req.valid("json");

      // Verify organization membership
      const membership = await getOrganizationMembership(session.userId, organizationId);
      if (!membership) {
        throw new HTTPException(403, { message: "You don't have access to this organization" });
      }

      // Find the transaction
      const transaction = await db.transaction.findFirst({
        where: {
          id: transactionId,
          organizationId: organizationId,
        },
      });

      if (!transaction) {
        throw new HTTPException(404, { message: "Transaction not found" });
      }

      // Update the transaction with the external ID
      const updatedTransaction = await db.transaction.update({
        where: { id: transactionId },
        data: {
          externalId: externalId,
          metadata: {
            ...transaction.metadata,
            gatewayType: gatewayType,
            externalIdMappedAt: new Date().toISOString(),
          },
        },
      });

      logger.info("External ID mapped successfully", {
        transactionId,
        externalId,
        gatewayType,
        organizationId,
      });

      return c.json({
        success: true,
        message: "External ID mapped successfully",
        transaction: {
          id: updatedTransaction.id,
          externalId: updatedTransaction.externalId,
          status: updatedTransaction.status,
        },
      });
    } catch (error) {
      logger.error("Error mapping external ID", { error });

      if (error instanceof HTTPException) {
        throw error;
      }

      throw new HTTPException(500, { message: "Failed to map external ID" });
    }
  }
);
