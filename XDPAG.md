XdPag For Export
Order
POST
Create Payin
https://api.xdpag.com/api/order/pay-in
This API endpoint is used to initiate a payment for an order. When a POST request is sent to /api/order/pay-in, the request should include the amount, webhook, and externalId in the raw request body. Upon successful execution, the response will have a status code of 200 and a JSON content type. The response will include the data object with the id, status, externalId, brcode, and qrcode parameters.

Possible Status
CREATED - Initial state
PROCESSING - Intermediate state
FINISHED - Case transaction is FINISHED with success
CANCELLED - ''
REVERSED - ''
TIMEOUT - ''

Request Body
amount (string): The amount to be paid for the order.
webhook (string): The URL for the webhook to receive payment status updates.
externalId (string): The unique identifier for the order.
Response
data (object): An object containing the following parameters:
id (string): The identifier for the payment transaction.
status (string): The status of the payment transaction.
externalId (string): The unique identifier for the order.
brcode (string): The BR Code for the payment.
qrcode (string): The QR Code for the payment.
AUTHORIZATION
Bearer Token
Token
HEADERS




body

{
    "amount": 10, // REQUIRED
    "webhook": "https://google.com", // REQUIRED URL WITH HTTPS
    "externalId": "yout-id-here", // REQUIRED
    "description": "Description in pix transaction", // OPTIONAL
    "additional_data": [ // OPTIONAL
        {
            "name": "Atenção",
            "value": "Ao confirmar o PIX, você aceita que: 1) Valor vai para apostas; 2) Uso do valor é sua responsabilidade."
        }
    ]
}






POST
Create Payout
https://api.xdpag.com/api/order/pay-out
This endpoint is used to initiate a payout for an order via an HTTP POST request to /api/order/pay-out. The request should include the amount to be paid, a webhook URL, document information, a PIX key, and an external ID.

Possible Status
CREATED - Initial state
PROCESSING - Intermediate state
FINISHED - Case transaction is FINISHED with success
CANCELLED - Cancelled transaction
REVERSED - REFUND transaction
PARTIALLY_REVERSED - REFUND partially of transaction
TIMEOUT - ''

Request Body

amount (number): The amount to be paid.

webhook (string): The URL for the webhook.

document (string): Information related to the document.

pixKey (string): The PIX key for the payout.

externalId (string): An external ID associated with the payout.

Response
Upon successful execution, the API returns a 200 status with a JSON response containing the following data:

id (string): The ID of the payout.

status (string): The status of the payout.

externalId (string): The external ID associated with the payout.

events (array): An array of events related to the payout.

Pix Key Invalid
The api returns a 422 status error with JSON

json
{
    "message": "INVALID_PIX_KEY"
}
Account Block By Security Amount Limit
The api returns a 422 status error with JSON

json
{
    "message": "OUT_DISABLED_BY_SECURITY"
}
AUTHORIZATION
Bearer Token
Token
HEADERS
Accept
application/json

Body
raw (json)

{
    "amount": 2,
    "webhook": "https://url-your-site-for-webhook.com",
    "document": "********",
    "pixKey": "pixkey",
    "pixKeyType": "PHONE", // PHONE, CPF, EMAIL, CNPJ, EVP
    "externalId": "your-id-here",
    "validate_document": false // Default false - Should use for validate field document is equal to document of pixKey
}




GET
Get Payin
https://api.xdpag.com/api/order/pay-in/:id
This endpoint retrieves the payment details for a specific order identified by its ID.

Request
Method: GET
Endpoint: /api/order/pay-in/:id
Response
Status: 200
Content-Type: application/json


{
    "data": {
        "id": "",
        "status": "",
        "amount": "",
        "fee": 0,
        "webhook": "",
        "externalId": "",
        "endToEndId": "",
        "transactionReceiptUrl": ""
    }
}



The response contains the payment details including the ID, status, amount, fee, webhook, external ID, end-to-end ID, and transaction receipt URL for the specified order.

AUTHORIZATION
Bearer Token
Token
HEADERS
Accept
application/json

PATH VARIABLES
id
e83e1c2e-8ca4-4557-a1fe-33fb8e6a1d82

Example Request
Get Payin
View More
curl
curl --location 'https://api.xdpag.com/api/order/pay-in/e83e1c2e-8ca4-4557-a1fe-33fb8e6a1d82' \
--header 'Accept: application/json' \
--data ''
Example Response
Body
Headers (0)
No response body
This request doesn't return any response body
GET
Get Payout
https://api.xdpag.com/api/order/pay-out/:id
This API endpoint makes an HTTP GET request to retrieve the payout details for a specific order identified by the provided ID.

The response to the last execution of this request had a status code of 200 and a content type of application/json. The response body included the following fields:

id: The ID of the payout.
status: The status of the payout.
amount: The amount of the payout.
fee: The fee associated with the payout.
webhook: The webhook URL associated with the payout.
externalId: The external ID associated with the payout.
endToEndId: The end-to-end ID associated with the payout.
transactionReceiptUrl: The URL for the transaction receipt.
AUTHORIZATION
Bearer Token




GET
Get Balance
https://api.xdpag.com/api/order/balance
This endpoint makes an HTTP GET request to retrieve the balance information for an order. The response will be in JSON format with the following structure:

json
{
    "data": {
        "balance": 0,
        "balanceProvisioned": 0, // amount availabel for cashout
        "currency": ""
    }
}
The balance field represents the current balance, balanceProvisioned indicates the provisioned balance, and currency specifies the currency type.

AUTHORIZATION
Bearer Token
Token
HEADERS
Accept

