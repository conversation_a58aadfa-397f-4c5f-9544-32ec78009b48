# 🎯 PROMPT PARA AGENTE - CORREÇÃO DE TAXAS E TESTES

## **CONTEXTO DO PROBLEMA:**

Estamos trabalhando em um sistema de pagamentos PIX que processa transações via API `/api/payments/transactions`. O sistema está funcionando perfeitamente em termos de:

✅ **Duplicação**: ZERO duplicações - Sistema robusto
✅ **Performance**: 100% sucesso - Sistema estável
✅ **Cálculo de Taxas**: Funcionando quando executado manualmente

### **PROBLEMA CRÍTICO IDENTIFICADO:**

❌ **As taxas não estão sendo aplicadas automaticamente** no router da API porque o `organizationId` está chegando como `undefined` no processamento de taxas.

## **ARQUIVOS PRINCIPAIS:**

1. **`packages/api/src/routes/payments/transactions/router.ts`** - Router principal da API
2. **`packages/payments/src/taxes/calculator.ts`** - Cálculo de taxas (já corrigido)
3. **`packages/payments/src/taxes/fee-service.ts`** - Serviço de processamento de taxas
4. **`scripts/test-real-payment-processor-small.ts`** - Script de teste com valores pequenos
5. **`scripts/fix-organization-id-issue.ts`** - Script para corrigir o problema

## **TAREFAS PARA O AGENTE:**

### **1. CORRIGIR O PROBLEMA DO ORGANIZATIONID:**
- Verificar por que `organizationId` está chegando como `undefined` no processamento de taxas
- O código de processamento de taxas está no router em `packages/api/src/routes/payments/transactions/router.ts` (linhas 445-521)
- Garantir que o `organizationId` seja passado corretamente para `processTransactionFees`

### **2. EXECUTAR SCRIPT DE CORREÇÃO:**
```bash
npx tsx scripts/fix-organization-id-issue.ts
```

### **3. EXECUTAR TESTES COM VALORES PEQUENOS:**
```bash
npx tsx scripts/test-real-payment-processor-small.ts
```

## **CREDENCIAIS DE TESTE:**
```
API Key: pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9
Organization ID: fC99w8SdDGbNJM_q0b2s5
```

## **CONFIGURAÇÃO DE TAXAS:**
- Taxa %: 1% (da organização)
- Taxa Fixa: R$ 2,00 (da organização)
- Prioridade: SEMPRE usar taxas da organização (não do gateway)

## **VALORES DE TESTE:**
- Valores entre R$ 1,00 e R$ 5,00 (100 a 500 centavos)
- Nomes e CPFs realistas (não parecer teste)
- Emails realistas

## **RESULTADO ESPERADO:**
- Taxas aplicadas corretamente em todas as transações
- Zero duplicações
- 100% de sucesso
- Valores de teste entre R$ 1 e R$ 5

## **IMPACTO FINANCEIRO:**
- **Perda atual**: R$ 50.101/dia (20.202 transações × R$ 2,48)
- **Perda mensal**: R$ 1.503.030
- **Perda anual**: R$ 18.036.360

## **COMANDOS PARA EXECUTAR EM SEQUÊNCIA:**

```bash
# 1. Verificar se API está rodando
curl http://localhost:3000/api/health

# 2. Executar script de correção
npx tsx scripts/fix-organization-id-issue.ts

# 3. Executar testes com valores pequenos
npx tsx scripts/test-real-payment-processor-small.ts

# 4. Verificar transações no banco
npx tsx -e "
import { config } from 'dotenv';
import { resolve } from 'path';
config({ path: resolve(process.cwd(), '.env') });
import { db } from './packages/database';

async function checkTransactions() {
  const txs = await db.transaction.findMany({
    where: { organizationId: 'fC99w8SdDGbNJM_q0b2s5' },
    select: { id: true, amount: true, totalFee: true, percentFee: true, fixedFee: true },
    orderBy: { createdAt: 'desc' },
    take: 10
  });
  console.log('Últimas 10 transações:', txs);
}

checkTransactions();
"
```

## **SISTEMA ATUAL:**
- **95% pronto para produção**
- **Só precisa da correção do organizationId**
- **Sistema robusto contra duplicações**
- **Performance excelente**

**O sistema está excelente em termos de duplicação e performance, só precisa da correção do organizationId!**
