#!/usr/bin/env tsx

/**
 * Teste Realista de Processador de Pagamento
 * Simula transações reais com nomes de pessoas e CPFs válidos
 * Testa volume de 2 milhões/dia com ticket médio R$ 99
 * Valida se taxas estão sendo salvas no banco
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// Carregar .env da raiz do projeto
config({ path: resolve(process.cwd(), '.env') });

interface RealPaymentTestResult {
  testName: string;
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  duplicateIds: number;
  duplicateQRCodes: number;
  duplicateReferenceCodes: number;
  uniqueIds: Set<string>;
  uniqueQRCodes: Set<string>;
  uniqueReferenceCodes: Set<string>;
  duration: number;
  averageResponseTime: number;
  errors: string[];
  transactionsWithFees: number;
  transactionsWithoutFees: number;
  feeIssues: string[];
}

class RealPaymentProcessorTest {
  private baseUrl = 'http://localhost:3000';
  private apiKey = 'pk_live_abkqxeDuJ65SxXkh_oPY9dwv2fN_b1s9';
  private organizationId = 'fC99w8SdDGbNJM_q0b2s5';
  private results: RealPaymentTestResult[] = [];

  // Nomes reais brasileiros para simular transações reais
  private realNames = [
    'João Silva Santos', 'Maria Oliveira Costa', 'Pedro Almeida Lima', 'Ana Paula Rodrigues',
    'Carlos Eduardo Ferreira', 'Fernanda Souza Martins', 'Rafael Barbosa Silva', 'Juliana Pereira Santos',
    'Marcos Antonio Oliveira', 'Camila Fernandes Costa', 'Lucas Gabriel Lima', 'Beatriz Santos Almeida',
    'Diego Rodrigues Pereira', 'Larissa Costa Silva', 'Thiago Oliveira Martins', 'Gabriela Souza Santos',
    'Bruno Almeida Costa', 'Isabella Lima Rodrigues', 'Felipe Santos Oliveira', 'Mariana Costa Silva',
    'André Pereira Almeida', 'Natália Souza Lima', 'Rodrigo Santos Costa', 'Amanda Oliveira Silva',
    'Vinícius Lima Santos', 'Carolina Costa Almeida', 'Guilherme Silva Oliveira', 'Letícia Santos Lima',
    'Henrique Costa Silva', 'Priscila Almeida Santos', 'Daniel Lima Costa', 'Renata Silva Oliveira',
    'Eduardo Santos Lima', 'Vanessa Costa Almeida', 'Fábio Oliveira Silva', 'Tatiana Lima Santos',
    'Gustavo Silva Costa', 'Patrícia Almeida Lima', 'Leandro Santos Oliveira', 'Cristina Costa Silva',
    'Paulo Lima Santos', 'Simone Silva Almeida', 'Roberto Costa Lima', 'Adriana Santos Silva',
    'Antonio Oliveira Costa', 'Sandra Lima Santos', 'José Silva Almeida', 'Márcia Costa Lima',
    'Francisco Santos Silva', 'Rita Almeida Costa', 'Manuel Lima Santos', 'Teresa Silva Oliveira'
  ];

  // CPFs válidos (apenas para teste - não são reais)
  private validCPFs = [
    '11111111111', '22222222222', '33333333333', '44444444444', '55555555555',
    '66666666666', '77777777777', '88888888888', '99999999999', '12345678901',
    '23456789012', '34567890123', '45678901234', '56789012345', '67890123456',
    '78901234567', '89012345678', '90123456789', '01234567890', '10987654321',
    '21098765432', '32109876543', '43210987654', '54321098765', '65432109876',
    '76543210987', '87654321098', '98765432109', '09876543210', '13579246801',
    '24680135792', '35792468013', '46801357924', '57924680135', '68013579246',
    '79135792468', '80246801357', '91357924680', '02468013579', '13579246802',
    '24680235791', '35791346802', '46802457913', '57913568024', '68024679135',
    '79135780246', '80246891357', '91357902468', '02468013579', '13579246803'
  ];

  // Emails reais para simular clientes reais
  private realEmails = [
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>', '<EMAIL>', '<EMAIL>',
    '<EMAIL>'
  ];

  // Telefones reais para simular clientes reais
  private realPhones = [
    '11999887766', '11988776655', '11977665544', '11966554433', '11955443322',
    '11944332211', '11933221100', '11922110099', '11911009988', '11900998877',
    '21999887766', '21988776655', '21977665544', '21966554433', '21955443322',
    '21944332211', '21933221100', '21922110099', '21911009988', '21900998877',
    '31999887766', '31988776655', '31977665544', '31966554433', '31955443322',
    '31944332211', '31933221100', '31922110099', '31911009988', '31900998877',
    '41999887766', '41988776655', '41977665544', '41966554433', '41955443322',
    '41944332211', '41933221100', '41922110099', '41911009988', '41900998877',
    '51999887766', '51988776655', '51977665544', '51966554433', '51955443322',
    '51944332211', '51933221100', '51922110099', '51911009988', '51900998877'
  ];

  async runRealPaymentProcessorTest(): Promise<void> {
    console.log('🏦 TESTE REALISTA DE PROCESSADOR DE PAGAMENTO');
    console.log('='.repeat(80));
    console.log('Simulando transações reais com nomes, CPFs e emails reais');
    console.log('Volume: R$ 2.000.000/dia | Ticket médio: R$ 99 | ~20.202 transações/dia');
    console.log('');

    // Verificar se API está rodando
    await this.checkApiHealth();

    // Teste 1: Volume normal (21 transações - 1 minuto de pico)
    await this.testRealisticVolume(21, 'Volume Normal (1 minuto de pico)');

    // Teste 2: Volume de pico (42 transações - 2 minutos de pico)
    await this.testRealisticVolume(42, 'Volume de Pico (2 minutos de pico)');

    // Teste 3: Volume crítico (63 transações - 3 minutos de pico)
    await this.testRealisticVolume(63, 'Volume Crítico (3 minutos de pico)');

    // Teste 4: Volume extremo (105 transações - 5 minutos de pico)
    await this.testRealisticVolume(105, 'Volume Extremo (5 minutos de pico)');

    // Análise de taxas
    await this.analyzeFeeIssues();

    // Resultados finais
    this.printFinalResults();
  }

  /**
   * Verificar se API está rodando
   */
  private async checkApiHealth(): Promise<void> {
    console.log('🔍 Verificando se API está rodando...');

    try {
      const response = await fetch(`${this.baseUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        console.log(`✅ API está rodando: ${response.status}`);
      } else {
        console.log(`⚠️ API respondeu com status: ${response.status}`);
      }

    } catch (error) {
      console.log(`❌ API não está rodando: ${error instanceof Error ? error.message : error}`);
      console.log('   Certifique-se de que o projeto está rodando na porta 3000');
    }

    console.log('');
  }

  /**
   * Teste de volume realista
   */
  private async testRealisticVolume(concurrentRequests: number, testName: string): Promise<void> {
    console.log(`🔄 ${testName} (${concurrentRequests} transações simultâneas)...`);

    const startTime = Date.now();
    const uniqueIds = new Set<string>();
    const uniqueQRCodes = new Set<string>();
    const uniqueReferenceCodes = new Set<string>();
    const errors: string[] = [];
    const feeIssues: string[] = [];
    let successfulTransactions = 0;
    let failedTransactions = 0;
    let transactionsWithFees = 0;
    let transactionsWithoutFees = 0;

    // Criar array de promessas para execução simultânea
    const promises = Array.from({ length: concurrentRequests }, (_, index) => 
      this.createRealisticTransaction(index, concurrentRequests)
    );

    try {
      // Executar todas as requisições simultaneamente
      const results = await Promise.allSettled(promises);

      // Processar resultados
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successfulTransactions++;
          const data = result.value.data;
          
          // Verificar unicidade de IDs
          if (data?.id) {
            if (uniqueIds.has(data.id)) {
              console.log(`⚠️ ID DUPLICADO: ${data.id} (índice ${index})`);
            } else {
              uniqueIds.add(data.id);
            }
          }
          
          // Verificar unicidade de Reference Codes
          if (data?.referenceCode) {
            if (uniqueReferenceCodes.has(data.referenceCode)) {
              console.log(`⚠️ REFERENCE CODE DUPLICADO: ${data.referenceCode} (índice ${index})`);
            } else {
              uniqueReferenceCodes.add(data.referenceCode);
            }
          }

          // Verificar unicidade de QR Codes
          if (data?.pix?.qrCode?.emv) {
            if (uniqueQRCodes.has(data.pix.qrCode.emv)) {
              console.log(`⚠️ QR CODE DUPLICADO: ${data.pix.qrCode.emv.substring(0, 50)}... (índice ${index})`);
            } else {
              uniqueQRCodes.add(data.pix.qrCode.emv);
            }
          }

          // Verificar se taxas estão sendo aplicadas
          if (data?.totalFee !== undefined && data.totalFee > 0) {
            transactionsWithFees++;
          } else {
            transactionsWithoutFees++;
            feeIssues.push(`Transação ${data?.id}: totalFee = ${data?.totalFee}, fixedFee = ${data?.fixedFee}, percentFee = ${data?.percentFee}`);
          }

        } else {
          failedTransactions++;
          const error = result.status === 'rejected' 
            ? result.reason 
            : result.value.error || 'Unknown error';
          errors.push(`Request ${index}: ${error}`);
        }
      });

      const duration = Date.now() - startTime;
      const averageResponseTime = duration / concurrentRequests;
      const duplicateIds = concurrentRequests - uniqueIds.size;
      const duplicateQRCodes = concurrentRequests - uniqueQRCodes.size;
      const duplicateReferenceCodes = concurrentRequests - uniqueReferenceCodes.size;

      this.results.push({
        testName,
        totalTransactions: concurrentRequests,
        successfulTransactions,
        failedTransactions,
        duplicateIds,
        duplicateQRCodes,
        duplicateReferenceCodes,
        uniqueIds,
        uniqueQRCodes,
        uniqueReferenceCodes,
        duration,
        averageResponseTime,
        errors,
        transactionsWithFees,
        transactionsWithoutFees,
        feeIssues
      });

      // Log do resultado
      const successRate = (successfulTransactions / concurrentRequests) * 100;
      const duplicateRate = (duplicateIds / concurrentRequests) * 100;
      const feeRate = (transactionsWithFees / successfulTransactions) * 100;

      console.log(`✅ ${testName}: CONCLUÍDO`);
      console.log(`   Total: ${concurrentRequests} | Sucesso: ${successfulTransactions} | Falhas: ${failedTransactions}`);
      console.log(`   Taxa de Sucesso: ${successRate.toFixed(1)}%`);
      console.log(`   IDs Únicos: ${uniqueIds.size} | Duplicados: ${duplicateIds}`);
      console.log(`   QR Codes Únicos: ${uniqueQRCodes.size} | Duplicados: ${duplicateQRCodes}`);
      console.log(`   Reference Codes Únicos: ${uniqueReferenceCodes.size} | Duplicados: ${duplicateReferenceCodes}`);
      console.log(`   Transações com Taxa: ${transactionsWithFees} | Sem Taxa: ${transactionsWithoutFees}`);
      console.log(`   Taxa de Aplicação de Taxas: ${feeRate.toFixed(1)}%`);
      console.log(`   Duração: ${duration}ms | Tempo Médio: ${averageResponseTime.toFixed(0)}ms`);

      if (duplicateIds > 0 || duplicateQRCodes > 0 || duplicateReferenceCodes > 0) {
        console.log(`   🚨 CRÍTICO: Duplicações detectadas!`);
      }

      if (transactionsWithoutFees > 0) {
        console.log(`   ⚠️ PROBLEMA: ${transactionsWithoutFees} transações sem taxa aplicada!`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.results.push({
        testName,
        totalTransactions: concurrentRequests,
        successfulTransactions: 0,
        failedTransactions: concurrentRequests,
        duplicateIds: 0,
        duplicateQRCodes: 0,
        duplicateReferenceCodes: 0,
        uniqueIds: new Set(),
        uniqueQRCodes: new Set(),
        uniqueReferenceCodes: new Set(),
        duration,
        averageResponseTime: 0,
        errors: [errorMessage],
        transactionsWithFees: 0,
        transactionsWithoutFees: 0,
        feeIssues: []
      });

      console.log(`❌ ${testName}: FALHOU - ${errorMessage}`);
    }

    console.log('');
  }

  /**
   * Criar uma transação realista
   */
  private async createRealisticTransaction(index: number, total: number): Promise<{
    success: boolean;
    data?: any;
    error?: string;
    duration?: number;
  }> {
    const startTime = Date.now();
    
    // Selecionar dados reais aleatórios
    const name = this.realNames[index % this.realNames.length];
    const cpf = this.validCPFs[index % this.validCPFs.length];
    const email = this.realEmails[index % this.realEmails.length];
    const phone = this.realPhones[index % this.realPhones.length];
    
    // Valor realista entre R$ 50 e R$ 200 (ticket médio R$ 99)
    const amount = 50 + Math.floor(Math.random() * 150);
    
    // Descrição realista
    const descriptions = [
      'Pagamento de serviços', 'Compra online', 'Pagamento de conta', 'Transferência PIX',
      'Pagamento de boleto', 'Compra em loja', 'Pagamento de frete', 'Serviço de entrega',
      'Pagamento de mensalidade', 'Compra de produto', 'Pagamento de taxa', 'Serviço de consultoria'
    ];
    const description = descriptions[index % descriptions.length];

    try {
      const requestBody = {
        amount,
        customerName: name,
        customerEmail: email,
        customerPhone: phone,
        customerDocument: cpf,
        customerDocumentType: 'cpf' as const,
        description: `${description} - ${name}`,
        organizationId: this.organizationId,
        metadata: {
          source: 'real_payment_processor',
          testIndex: index,
          totalTests: total,
          timestamp: Date.now(),
          customerType: 'real_customer',
          paymentMethod: 'pix_charge'
        }
      };

      const response = await fetch(`${this.baseUrl}/api/payments/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey
        },
        body: JSON.stringify(requestBody)
      });

      const duration = Date.now() - startTime;
      const responseData = await response.json();

      if (response.ok) {
        return {
          success: true,
          data: responseData,
          duration
        };
      } else {
        return {
          success: false,
          error: `API Error ${response.status}: ${responseData.message || 'Unknown error'}`,
          duration
        };
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration
      };
    }
  }

  /**
   * Analisar problemas de taxas
   */
  private async analyzeFeeIssues(): Promise<void> {
    console.log('💰 ANÁLISE DE PROBLEMAS DE TAXAS');
    console.log('-'.repeat(60));

    const allFeeIssues = this.results.flatMap(r => r.feeIssues);
    const totalTransactions = this.results.reduce((sum, r) => sum + r.successfulTransactions, 0);
    const totalWithoutFees = this.results.reduce((sum, r) => sum + r.transactionsWithoutFees, 0);

    console.log(`Total de Transações Analisadas: ${totalTransactions}`);
    console.log(`Transações sem Taxa: ${totalWithoutFees}`);
    console.log(`Taxa de Aplicação de Taxas: ${((totalTransactions - totalWithoutFees) / totalTransactions * 100).toFixed(1)}%`);

    if (allFeeIssues.length > 0) {
      console.log('\n🔍 PROBLEMAS DETECTADOS:');
      allFeeIssues.slice(0, 10).forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
      
      if (allFeeIssues.length > 10) {
        console.log(`   ... e mais ${allFeeIssues.length - 10} problemas`);
      }
    }

    console.log('\n🔧 POSSÍVEIS CAUSAS:');
    console.log('   1. Taxas não configuradas para a organização');
    console.log('   2. Gateway não aplicando taxas automaticamente');
    console.log('   3. Código de processamento de taxas não executando');
    console.log('   4. Configuração de taxas incorreta no banco de dados');
    console.log('   5. Provider não retornando informações de taxa');

    console.log('\n💡 RECOMENDAÇÕES:');
    console.log('   1. Verificar configuração de taxas da organização');
    console.log('   2. Implementar processamento automático de taxas');
    console.log('   3. Adicionar logs de debug para processamento de taxas');
    console.log('   4. Validar se gateway está aplicando taxas corretamente');
    console.log('   5. Implementar fallback para aplicação manual de taxas');
  }

  /**
   * Imprimir resultados finais
   */
  private printFinalResults(): void {
    console.log('\n📊 RESULTADOS FINAIS DO TESTE REALISTA');
    console.log('='.repeat(80));

    const totalTests = this.results.length;
    const totalTransactions = this.results.reduce((sum, r) => sum + r.totalTransactions, 0);
    const totalSuccessful = this.results.reduce((sum, r) => sum + r.successfulTransactions, 0);
    const totalFailed = this.results.reduce((sum, r) => sum + r.failedTransactions, 0);
    const totalDuplicateIds = this.results.reduce((sum, r) => sum + r.duplicateIds, 0);
    const totalDuplicateQRCodes = this.results.reduce((sum, r) => sum + r.duplicateQRCodes, 0);
    const totalDuplicateReferenceCodes = this.results.reduce((sum, r) => sum + r.duplicateReferenceCodes, 0);
    const totalWithFees = this.results.reduce((sum, r) => sum + r.transactionsWithFees, 0);
    const totalWithoutFees = this.results.reduce((sum, r) => sum + r.transactionsWithoutFees, 0);

    console.log(`Total de Testes: ${totalTests}`);
    console.log(`Total de Transações: ${totalTransactions}`);
    console.log(`Sucessos: ${totalSuccessful} (${Math.round(totalSuccessful / totalTransactions * 100)}%)`);
    console.log(`Falhas: ${totalFailed} (${Math.round(totalFailed / totalTransactions * 100)}%)`);
    console.log(`IDs Duplicados: ${totalDuplicateIds}`);
    console.log(`QR Codes Duplicados: ${totalDuplicateQRCodes}`);
    console.log(`Reference Codes Duplicados: ${totalDuplicateReferenceCodes}`);
    console.log(`Transações com Taxa: ${totalWithFees}`);
    console.log(`Transações sem Taxa: ${totalWithoutFees}`);

    // Análise de risco
    console.log('\n🔒 ANÁLISE DE RISCO PARA PRODUÇÃO');
    console.log('-'.repeat(60));

    if (totalDuplicateIds === 0 && totalDuplicateQRCodes === 0 && totalDuplicateReferenceCodes === 0) {
      console.log('✅ RISCO DE DUPLICAÇÃO: BAIXO');
      console.log('   Sistema garante unicidade de identificadores');
    } else {
      console.log('❌ RISCO DE DUPLICAÇÃO: CRÍTICO');
      console.log(`   ${totalDuplicateIds} IDs duplicados`);
      console.log(`   ${totalDuplicateQRCodes} QR Codes duplicados`);
      console.log(`   ${totalDuplicateReferenceCodes} Reference Codes duplicados`);
    }

    if (totalWithoutFees === 0) {
      console.log('✅ RISCO DE TAXAS: BAIXO');
      console.log('   Todas as transações tiveram taxas aplicadas');
    } else {
      console.log('❌ RISCO DE TAXAS: ALTO');
      console.log(`   ${totalWithoutFees} transações sem taxa aplicada`);
      console.log('   Perda de receita significativa');
    }

    if (totalSuccessful / totalTransactions >= 0.95) {
      console.log('✅ RISCO DE DISPONIBILIDADE: BAIXO');
      console.log('   Alta taxa de sucesso (>95%)');
    } else {
      console.log('⚠️ RISCO DE DISPONIBILIDADE: MÉDIO');
      console.log('   Taxa de sucesso pode ser melhorada');
    }

    // Recomendação final
    console.log('\n🎯 RECOMENDAÇÃO FINAL');
    console.log('-'.repeat(60));

    if (totalDuplicateIds === 0 && totalDuplicateQRCodes === 0 && totalDuplicateReferenceCodes === 0 && totalWithoutFees === 0) {
      console.log('🎉 SISTEMA APROVADO PARA PRODUÇÃO!');
      console.log('✅ Nenhuma duplicação detectada');
      console.log('✅ Todas as taxas aplicadas corretamente');
      console.log('✅ Sistema robusto para volume real');
    } else {
      console.log('❌ SISTEMA NÃO APROVADO PARA PRODUÇÃO');
      if (totalDuplicateIds > 0 || totalDuplicateQRCodes > 0 || totalDuplicateReferenceCodes > 0) {
        console.log('❌ Corrigir problema de duplicação');
      }
      if (totalWithoutFees > 0) {
        console.log('❌ Corrigir problema de aplicação de taxas');
      }
    }
  }
}

// Executar teste se script for chamado diretamente
if (require.main === module) {
  const tester = new RealPaymentProcessorTest();
  tester.runRealPaymentProcessorTest()
    .catch(console.error)
    .finally(() => process.exit(0));
}

export { RealPaymentProcessorTest };
