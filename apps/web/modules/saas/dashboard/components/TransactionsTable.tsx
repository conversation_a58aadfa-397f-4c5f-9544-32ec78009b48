"use client";

import { useTranslations } from "next-intl";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { useState } from "react";
import { TransactionDetails, TransactionDetailsSheet } from "@saas/transactions/components/TransactionDetailsSheet";
import { DataTable } from "@saas/shared/components/DataTable";
import {
  getStatusBadgeVariant,
  getStatusDisplayText,
  isApprovedStatus,
  isPendingStatus
} from "@repo/utils";

type Transaction = TransactionDetails;

interface TransactionsTableProps {
  transactions: Transaction[];
  isLoading?: boolean;
}

export function TransactionsTable({ transactions, isLoading = false }: TransactionsTableProps) {
  const t = useTranslations();
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);

  const statusVariants = {
    approved: "bg-emerald-500/10 text-emerald-500 border-emerald-500/20",
    pending: "bg-amber-500/10 text-amber-500 border-amber-500/20",
    refused: "bg-rose-500/10 text-rose-500 border-rose-500/20",
    refunded: "bg-violet-500/10 text-violet-500 border-violet-500/20",
    default: "bg-gray-500/10 text-gray-500 border-gray-500/20"
  };

  function getStatusVariant(status: string) {
    return getStatusBadgeVariant(status);
  }

  const handleRowClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
  };

  if (isLoading) {
    return <div className="text-center py-6 text-muted-foreground">Carregando transações...</div>;
  }

  return (
    <>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-800 text-left">
              <th className="pb-2 font-medium text-muted-foreground text-sm">Transação</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Cliente</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Descrição</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Data</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Data Pagamento</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Valor</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Status</th>
              <th className="pb-2 font-medium text-muted-foreground text-sm">Ações</th>
            </tr>
          </thead>
          <tbody>
            {transactions.map((transaction) => (
              <tr
                key={transaction.id}
                className="border-b border-gray-800/50 hover:bg-gray-800/30 dark:hover:bg-gray-800/30 cursor-pointer transition-colors"
                onClick={() => handleRowClick(transaction)}
              >
                <td className="py-3 text-sm">
                  <div className="max-w-[180px] truncate font-mono text-xs">{transaction.id}</div>
                </td>
                <td className="py-3 text-sm">
                  <div className="max-w-[180px]">
                    <div className="truncate font-medium">{transaction.customerName}</div>
                    <div className="truncate text-xs text-muted-foreground">{transaction.customerEmail}</div>
                  </div>
                </td>
                <td className="py-3 text-sm">
                  <div className="max-w-[150px]">
                    {transaction.description ? (
                      <span className="text-sm text-gray-300 truncate block" title={transaction.description}>
                        {transaction.description}
                      </span>
                    ) : (
                      <span className="text-xs text-muted-foreground">-</span>
                    )}
                  </div>
                </td>
                <td className="py-3 text-sm">{transaction.createdAt}</td>
                <td className="py-3 text-sm">{transaction.paymentAt || "-"}</td>
                <td className="py-3 text-sm">{transaction.amount}</td>
                <td className="py-3 text-sm">
                  {(() => {
                    const status = transaction.status;
                    const statusClass = getStatusVariant(status);

                    return (
                      <Badge
                        status={statusClass}
                        className={cn("uppercase px-2 py-1 text-xs font-semibold")}
                      >
                        {getStatusDisplayText(status)}
                      </Badge>
                    );
                  })()}
                </td>
                <td className="py-3 text-sm">
                  <div className="text-right">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedTransaction(transaction);
                      }}
                      className="inline-flex size-8 items-center justify-center rounded-md border border-gray-800 bg-gray-950/50 text-sm font-medium shadow-sm transition-colors hover:bg-gray-700 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-gray-400"
                    >
                      <span className="sr-only">Ver detalhes</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="size-4"
                      >
                        <path d="M4 12h16"></path>
                        <path d="M12 4v16"></path>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {selectedTransaction && (
        <TransactionDetailsSheet
          isOpen={!!selectedTransaction}
          onClose={() => setSelectedTransaction(null)}
          transaction={selectedTransaction}
        />
      )}
    </>
  );
}
