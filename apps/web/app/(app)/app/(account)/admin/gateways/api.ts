"use server";

import { db } from "@repo/database";
import { GatewayData } from "./components/GatewayCardWrapper";

type GatewayFormData = {
  type: string;
  isActive: boolean;
  isDefault: boolean;
  priority: number;
  credentials: Record<string, any>;
  canReceive: boolean;
  canSend: boolean;
  pixChargePercentFee: number;
  pixTransferPercentFee: number;
  pixChargeFixedFee: number;
  pixTransferFixedFee: number;
};

export async function getGateways() {
  try {
    const gateways = await db.payment_gateway.findMany({
      orderBy: [
        { priority: 'asc' },
        { createdAt: 'desc' },
      ],
    });

    return { success: true, data: gateways };
  } catch (error) {
    console.error("Error fetching gateways:", error);
    return { success: false, error: "Failed to fetch gateways" };
  }
}

export async function getGateway(id: string) {
  try {
    const gateway = await db.payment_gateway.findUnique({
      where: { id },
    });

    if (!gateway) {
      return { success: false, error: "Gateway not found" };
    }

    return { success: true, data: gateway };
  } catch (error) {
    console.error(`Error fetching gateway ${id}:`, error);
    return { success: false, error: "Failed to fetch gateway" };
  }
}

export async function createGateway(data: GatewayFormData) {
  try {
    // If this gateway is being set as default, unset any others
    if (data.isDefault) {
      await db.payment_gateway.updateMany({
        where: { isDefault: true },
        data: { isDefault: false },
      });
    }

    const gateway = await db.payment_gateway.create({
      data: {
        name: getGatewayName(data.type),
        type: data.type,
        isActive: data.isActive,
        isDefault: data.isDefault,
        priority: data.priority,
        credentials: data.credentials,
        canReceive: data.canReceive,
        canSend: data.canSend,
        adminConfigured: true,
        isGlobal: true,
        organizationId: "global",
      },
    });

    return { success: true, data: gateway };
  } catch (error) {
    console.error("Error creating gateway:", error);
    return { success: false, error: "Failed to create gateway" };
  }
}

export async function updateGateway(id: string, data: Partial<GatewayFormData>) {
  try {
    const existingGateway = await db.payment_gateway.findUnique({
      where: { id },
    });

    if (!existingGateway) {
      return { success: false, error: "Gateway not found" };
    }

    // If this gateway is being set as default, unset any others
    if (data.isDefault) {
      await db.payment_gateway.updateMany({
        where: {
          id: { not: id },
          isDefault: true,
        },
        data: { isDefault: false },
      });
    }

    const updatedGateway = await db.payment_gateway.update({
      where: { id },
      data: {
        isActive: data.isActive,
        isDefault: data.isDefault,
        priority: data.priority,
        credentials: data.credentials,
        canReceive: data.canReceive,
        canSend: data.canSend,
      },
    });

    return { success: true, data: updatedGateway };
  } catch (error) {
    console.error(`Error updating gateway ${id}:`, error);
    return { success: false, error: "Failed to update gateway" };
  }
}

export async function deleteGateway(id: string) {
  try {
    const existingGateway = await db.payment_gateway.findUnique({
      where: { id },
    });

    if (!existingGateway) {
      return { success: false, error: "Gateway not found" };
    }

    // Don't allow deletion of default gateways
    if (existingGateway.isDefault) {
      return {
        success: false,
        error: "Cannot delete a gateway that is set as default",
      };
    }

    await db.payment_gateway.delete({
      where: { id },
    });

    return { success: true };
  } catch (error) {
    console.error(`Error deleting gateway ${id}:`, error);
    return { success: false, error: "Failed to delete gateway" };
  }
}

// Helper function to get gateway name from type
function getGatewayName(type: string): string {
  const gatewayTypes: Record<string, string> = {
    flow2pay: "Flow2Pay",
    mercadopago: "Mercado Pago",
    asaas: "Asaas",
    reflowpay: "ReflowPay",
    primepag: "PrimePag",
    pixium: "Pixium",
    transfeera: "Transfeera",
    mediuspag: "MediusPag",
    owempay: "Owempay",
    owempay_v2: "Owempay v2",
    pluggou_pix: "Pluggou PIX",
  };

  return gatewayTypes[type.toLowerCase()] || type;
}
