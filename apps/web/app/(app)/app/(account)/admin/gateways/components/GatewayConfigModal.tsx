"use client";

import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@ui/components/dialog";
import { Separator } from "@ui/components/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Settings, CreditCard, Database, AlertCircle, Info } from "lucide-react";
import Image from "next/image";
import { Alert, AlertDescription } from "@ui/components/alert";

import { GatewayInfo, GatewayData } from "./GatewayCardWrapper";

const gatewaySchema = z.object({
  type: z.string(),
  displayName: z.string().optional(),
  description: z.string().optional(),
  instanceNumber: z.coerce.number().int().min(1).default(1),
  isActive: z.boolean().default(false),
  isDefault: z.boolean().default(false),
  priority: z.coerce.number().int().min(0).default(999),
  credentials: z.record(z.any()).default({}),
  canReceive: z.boolean().default(true),
  canSend: z.boolean().default(false),
  pixChargePercentFee: z.coerce.number().min(0).max(100).default(0),
  pixTransferPercentFee: z.coerce.number().min(0).max(100).default(0),
  pixChargeFixedFee: z.coerce.number().min(0).default(0),
  pixTransferFixedFee: z.coerce.number().min(0).default(0),
}).refine((data) => {
  // Validações específicas por tipo de gateway
  if (data.type.toLowerCase() === 'zendry') {
    const credentials = data.credentials;
    return credentials.clientId && credentials.clientSecret && credentials.environment;
  }
  if (data.type.toLowerCase() === 'mediuspag') {
    const credentials = data.credentials;
    return credentials.companyId;
  }
  if (data.type.toLowerCase() === 'ecomovi') {
    const credentials = data.credentials;
    return credentials.clientId && credentials.clientSecret && credentials.pixKey && credentials.environment;
  }
  if (data.type.toLowerCase() === 'cartwave') {
    const credentials = data.credentials;
    return credentials.apiKey;
  }
  if (data.type.toLowerCase() === 'owempay') {
    const credentials = data.credentials;
    return credentials.clientId && credentials.clientSecret && credentials.environment;
  }
  if (data.type.toLowerCase() === 'owempay_v2') {
    const credentials = data.credentials;
    return credentials.apiKey && credentials.apiSecret && credentials.environment;
  }
  if (data.type.toLowerCase() === 'zeitbank') {
    const credentials = data.credentials;
    // Para ZeitBank, precisamos das credenciais de PIX e opcionalmente das de transferência
    return credentials.pixClientId && credentials.pixClientSecret && credentials.pixKey && credentials.environment;
  }
  if (data.type.toLowerCase() === 'xdpag') {
    const credentials = data.credentials;
    return credentials.username && credentials.password && credentials.environment;
  }
  return true;
}, {
  message: "Credenciais obrigatórias não fornecidas",
  path: ["credentials"]
});

type GatewayFormData = z.infer<typeof gatewaySchema>;

export interface GatewayConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gateway: GatewayInfo;
  gatewayData?: GatewayData;
  isConfigured: boolean;
  onSuccess?: () => void;
}

export const GatewayConfigModal = ({
  open,
  onOpenChange,
  gateway,
  gatewayData,
  isConfigured,
  onSuccess
}: GatewayConfigModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [existingInstances, setExistingInstances] = useState<number[]>([]);
  const [isCheckingInstances, setIsCheckingInstances] = useState(false);
  const queryClient = useQueryClient();
  const router = useRouter();

  // Garantir que credenciais existentes sejam carregadas corretamente
  const existingCredentials = gatewayData?.credentials ?
    (typeof gatewayData.credentials === 'string' ?
      JSON.parse(gatewayData.credentials) :
      gatewayData.credentials) :
    {};

  // Buscar instâncias existentes do mesmo tipo
  const fetchExistingInstances = async (gatewayType: string) => {
    if (!isConfigured) {
      setIsCheckingInstances(true);
      try {
        const response = await fetch(`/api/admin/gateways?type=${gatewayType}`);
        if (response.ok) {
          const data = await response.json();
          const instances = data.gateways
            .filter((g: any) => g.type.toLowerCase() === gatewayType.toLowerCase())
            .map((g: any) => g.instanceNumber);
          setExistingInstances(instances);
        }
      } catch (error) {
        console.error("Erro ao buscar instâncias existentes:", error);
      } finally {
        setIsCheckingInstances(false);
      }
    }
  };

  // Calculate next instance number for new gateways
  const getNextInstanceNumber = () => {
    if (isConfigured) {
      return (gatewayData as any)?.instanceNumber || 1;
    }

    // Para novas instâncias, usar o próximo número disponível
    if (existingInstances.length > 0) {
      const maxInstance = Math.max(...existingInstances);
      return maxInstance + 1;
    }
    return 1;
  };

  const defaultValues: Partial<GatewayFormData> = {
    type: (gateway as any).originalType || gateway.id,
    displayName: (gatewayData as any)?.displayName || '',
    description: (gatewayData as any)?.description || '',
    instanceNumber: getNextInstanceNumber(),
    isActive: gatewayData?.isActive || false,
    isDefault: gatewayData?.isDefault || false,
    priority: gatewayData?.priority || 999,
    canReceive: gatewayData?.canReceive !== undefined ? gatewayData.canReceive : gateway.features?.pixReceive,
    canSend: gatewayData?.canSend !== undefined ? gatewayData.canSend : gateway.features?.pixSend,
    pixChargePercentFee: gatewayData?.pixChargePercentFee || 0,
    pixTransferPercentFee: gatewayData?.pixTransferPercentFee || 0,
    pixChargeFixedFee: gatewayData?.pixChargeFixedFee || 0,
    pixTransferFixedFee: gatewayData?.pixTransferFixedFee || 0,
    credentials: existingCredentials,
  };

  const form = useForm<GatewayFormData>({
    resolver: zodResolver(gatewaySchema),
    defaultValues,
  });

  // Atualizar o formulário quando os dados do gateway mudarem
  useEffect(() => {
    if (isConfigured && gatewayData) {
      form.reset({
        type: (gateway as any).originalType || gateway.id,
        displayName: (gatewayData as any)?.displayName || '',
        description: (gatewayData as any)?.description || '',
        instanceNumber: (gatewayData as any)?.instanceNumber || 1,
        isActive: gatewayData.isActive,
        isDefault: gatewayData.isDefault,
        priority: gatewayData.priority,
        canReceive: gatewayData.canReceive,
        canSend: gatewayData.canSend,
        pixChargePercentFee: 0,
        pixTransferPercentFee: 0,
        pixChargeFixedFee: 0,
        pixTransferFixedFee: 0,
        credentials: existingCredentials,
      });
    }
  }, [isConfigured, gatewayData, gateway.id, form, existingCredentials]);

  // Buscar instâncias existentes quando o modal abrir
  useEffect(() => {
    if (open && !isConfigured) {
      const gatewayType = (gateway as any).originalType || gateway.id;
      fetchExistingInstances(gatewayType);
    }
  }, [open, isConfigured, gateway]);

  // Atualizar número da instância quando instâncias existentes mudarem
  useEffect(() => {
    if (!isConfigured && existingInstances.length > 0) {
      const nextInstance = getNextInstanceNumber();
      form.setValue('instanceNumber', nextInstance);
    }
  }, [existingInstances, isConfigured, form]);

  // Função para excluir gateway
  const handleDelete = async () => {
    if (!isConfigured || !gatewayData?.id) {
      toast.error("Não é possível excluir um gateway não configurado");
      return;
    }

    // Confirmação antes de excluir
    const confirmed = window.confirm(
      `Tem certeza que deseja excluir a instância "${gatewayData.displayName || gateway.name}"?\n\n` +
      "Esta ação não pode ser desfeita e todas as configurações serão perdidas."
    );

    if (!confirmed) {
      return;
    }

    setIsDeleting(true);

    try {
      const response = await fetch(`/api/admin/gateways/${gatewayData.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const text = await response.text();
        let errorMessage = 'Erro ao excluir gateway';

        try {
          if (text) {
            const errorData = JSON.parse(text);
            errorMessage = errorData.error || errorData.message || errorMessage;
          }
        } catch (e) {
          errorMessage = response.statusText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['gateways'] });
      queryClient.invalidateQueries({ queryKey: ['admin-gateways'] });

      // Close the modal
      onOpenChange(false);

      // Notify success
      toast.success('Gateway excluído com sucesso');

      // Refresh the page
      router.refresh();

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error deleting gateway:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao excluir gateway');
    } finally {
      setIsDeleting(false);
    }
  };

  const onSubmit = async (data: GatewayFormData) => {
    console.log("🚀 onSubmit chamado com dados:", data);
    console.log("🔍 Validação do formulário:", form.formState.errors);

    // Verificar se há erros de validação
    if (Object.keys(form.formState.errors).length > 0) {
      console.error("❌ Erros de validação encontrados:", form.formState.errors);
      toast.error("Por favor, corrija os erros de validação antes de continuar");
      return;
    }

    // Verificar se a instância já existe (apenas para novas configurações)
    if (!isConfigured && existingInstances.includes(data.instanceNumber)) {
      toast.error(`A instância ${data.instanceNumber} já existe para este gateway. Use uma instância diferente.`);
      return;
    }

    setIsSubmitting(true);

    try {
      console.log("Enviando dados:", data);

      // Create or update gateway based on isConfigured flag
      const url = isConfigured
        ? `/api/admin/gateways/${gatewayData?.id}`
        : '/api/admin/gateways';

      const method = isConfigured ? 'PATCH' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        // First check if the response is valid JSON
        const text = await response.text();
        let errorMessage = 'Ocorreu um erro ao salvar o gateway';

        try {
          // Try to parse as JSON if possible
          if (text) {
            const errorData = JSON.parse(text);
            errorMessage = errorData.error || errorData.message || errorMessage;
          }
        } catch (e) {
          // If JSON parsing fails, use the response status text or a default message
          errorMessage = response.statusText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['gateways'] });
      queryClient.invalidateQueries({ queryKey: ['admin-gateways'] });

      // Close the modal
      onOpenChange(false);

      // Notify success
      toast.success(
        isConfigured ? 'Gateway atualizado com sucesso' : 'Gateway configurado com sucesso'
      );

      // Refresh the page
      router.refresh();

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving gateway:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao salvar o gateway');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderCredentialFields = () => {
    const gatewayType = form.watch('type')?.toLowerCase() || '';

    switch (gatewayType) {
      case 'zendry':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="clientId" className="text-sm font-medium">Client ID</Label>
                <Input
                  id="clientId"
                  {...form.register('credentials.clientId')}
                  placeholder="Digite o Client ID"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Identificador único fornecido pelo Zendry
                </p>
                {form.formState.errors.credentials?.clientId && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.clientId.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="clientSecret" className="text-sm font-medium">Client Secret</Label>
                <Input
                  id="clientSecret"
                  type="password"
                  {...form.register('credentials.clientSecret')}
                  placeholder="Digite o Client Secret"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave secreta para autenticação
                </p>
                {form.formState.errors.credentials?.clientSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.clientSecret.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="environment" className="text-sm font-medium">Ambiente</Label>
                <select
                  id="environment"
                  {...form.register('credentials.environment')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                >
                  <option value="">Selecione o ambiente</option>
                  <option value="sandbox">Sandbox (Testes)</option>
                  <option value="production">Produção</option>
                </select>
                <p className="text-xs text-muted-foreground mt-1">
                  Ambiente de operação do gateway
                </p>
                {form.formState.errors.credentials?.environment && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.environment.message?.toString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        );

      case 'pagarme':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="apiKey" className="text-sm font-medium">API Key</Label>
                <Input
                  id="apiKey"
                  type="password"
                  {...form.register('credentials.apiKey')}
                  placeholder="Digite a API Key do Pagar.me"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave de API fornecida pelo Pagar.me (sk_live_... ou sk_test_...)
                </p>
                {form.formState.errors.credentials?.apiKey && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.apiKey.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="environment" className="text-sm font-medium">Ambiente</Label>
                <select
                  id="environment"
                  {...form.register('credentials.environment')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                >
                  <option value="">Selecione o ambiente</option>
                  <option value="sandbox">Sandbox (Testes)</option>
                  <option value="production">Produção</option>
                </select>
                <p className="text-xs text-muted-foreground mt-1">
                  Ambiente de operação do Pagar.me
                </p>
                {form.formState.errors.credentials?.environment && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.environment.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="webhookSecret" className="text-sm font-medium">Webhook Secret (Opcional)</Label>
                <Input
                  id="webhookSecret"
                  type="password"
                  {...form.register('credentials.webhookSecret')}
                  placeholder="Digite o Webhook Secret"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave secreta para validação de webhooks (opcional)
                </p>
                {form.formState.errors.credentials?.webhookSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.webhookSecret.message?.toString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        );

      case 'cartwave':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="apiKey" className="text-sm font-medium">API Key</Label>
                <Input
                  id="apiKey"
                  type="password"
                  {...form.register('credentials.apiKey')}
                  placeholder="Digite a API Key"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave de API fornecida pelo Cartwave Hub
                </p>
                {form.formState.errors.credentials?.apiKey && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.apiKey.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="environment" className="text-sm font-medium">Ambiente</Label>
                <select
                  id="environment"
                  {...form.register('credentials.environment')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                >
                  <option value="">Selecione o ambiente</option>
                  <option value="sandbox">Sandbox (Testes)</option>
                  <option value="production">Produção</option>
                </select>
                <p className="text-xs text-muted-foreground mt-1">
                  Ambiente de operação do gateway (opcional)
                </p>
                {form.formState.errors.credentials?.environment && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.environment.message?.toString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        );

      case 'mediuspag':
        return (
          <div className="space-y-6">
            <div>
              <Label htmlFor="companyId" className="text-sm font-medium">Company ID</Label>
              <Input
                id="companyId"
                {...form.register('credentials.companyId')}
                placeholder="Digite o Company ID"
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Identificador da empresa no MediusPag
              </p>
              {form.formState.errors.credentials?.companyId && (
                <p className="text-sm text-red-500 mt-1">
                  {form.formState.errors.credentials.companyId.message?.toString()}
                </p>
              )}
            </div>
          </div>
        );

      case 'ecomovi':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="clientId" className="text-sm font-medium">Client ID</Label>
                <Input
                  id="clientId"
                  {...form.register('credentials.clientId')}
                  placeholder="00011122957903453000145"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Client ID fornecido pela Onz Finance/Ecomovi
                </p>
                {form.formState.errors.credentials?.clientId && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.clientId.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="clientSecret" className="text-sm font-medium">Client Secret</Label>
                <Input
                  id="clientSecret"
                  type="password"
                  {...form.register('credentials.clientSecret')}
                  placeholder="GUzYzIxNDgtYTRiNC00YjkxLWE4ODktN"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Client Secret fornecido pela Onz Finance/Ecomovi
                </p>
                {form.formState.errors.credentials?.clientSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.clientSecret.message?.toString()}
                  </p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="pixKey" className="text-sm font-medium">Chave PIX</Label>
                <Input
                  id="pixKey"
                  {...form.register('credentials.pixKey')}
                  placeholder="36ae341f-e369-4e46-ac4b-efd126a5b56e"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave PIX registrada no Ecomovi para recebimentos
                </p>
                {form.formState.errors.credentials?.pixKey && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.pixKey.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="environment" className="text-sm font-medium">Ambiente</Label>
                <select
                  id="environment"
                  {...form.register('credentials.environment')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                >
                  <option value="">Selecione o ambiente</option>
                  <option value="sandbox">Sandbox (Testes)</option>
                  <option value="production">Produção</option>
                </select>
                <p className="text-xs text-muted-foreground mt-1">
                  Ambiente de operação do gateway
                </p>
                {form.formState.errors.credentials?.environment && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.environment.message?.toString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        );

      case 'owempay':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="clientId" className="text-sm font-medium">Client ID</Label>
                <Input
                  id="clientId"
                  {...form.register('credentials.clientId')}
                  placeholder="Digite o Client ID"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Identificador único fornecido pela Owempay
                </p>
                {form.formState.errors.credentials?.clientId && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.clientId.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="clientSecret" className="text-sm font-medium">Client Secret</Label>
                <Input
                  id="clientSecret"
                  type="password"
                  {...form.register('credentials.clientSecret')}
                  placeholder="Digite o Client Secret"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave secreta para autenticação na API da Owempay
                </p>
                {form.formState.errors.credentials?.clientSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.clientSecret.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="environment" className="text-sm font-medium">Ambiente</Label>
                <select
                  id="environment"
                  {...form.register('credentials.environment')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                >
                  <option value="">Selecione o ambiente</option>
                  <option value="sandbox">Sandbox (Testes)</option>
                  <option value="production">Produção</option>
                </select>
                <p className="text-xs text-muted-foreground mt-1">
                  Ambiente de operação da Owempay
                </p>
                {form.formState.errors.credentials?.environment && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.environment.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="webhookSecret" className="text-sm font-medium">Webhook Secret (Opcional)</Label>
                <Input
                  id="webhookSecret"
                  type="password"
                  {...form.register('credentials.webhookSecret')}
                  placeholder="Digite o Webhook Secret"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave secreta para validação de webhooks (opcional)
                </p>
                {form.formState.errors.credentials?.webhookSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.webhookSecret.message?.toString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        );

      case 'owempay_v2':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="apiKey" className="text-sm font-medium">API Key</Label>
                <Input
                  id="apiKey"
                  {...form.register('credentials.apiKey')}
                  placeholder="Digite a API Key"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave de API fornecida pela Owempay v2
                </p>
                {form.formState.errors.credentials?.apiKey && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.apiKey.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="apiSecret" className="text-sm font-medium">API Secret</Label>
                <Input
                  id="apiSecret"
                  type="password"
                  {...form.register('credentials.apiSecret')}
                  placeholder="Digite a API Secret"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave secreta para autenticação na API v2 da Owempay
                </p>
                {form.formState.errors.credentials?.apiSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.apiSecret.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="environment" className="text-sm font-medium">Ambiente</Label>
                <select
                  id="environment"
                  {...form.register('credentials.environment')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                >
                  <option value="">Selecione o ambiente</option>
                  <option value="sandbox">Sandbox (Testes)</option>
                  <option value="production">Produção</option>
                </select>
                <p className="text-xs text-muted-foreground mt-1">
                  Ambiente de operação da Owempay v2
                </p>
                {form.formState.errors.credentials?.environment && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.environment.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="webhookSecret" className="text-sm font-medium">Webhook Secret (Opcional)</Label>
                <Input
                  id="webhookSecret"
                  type="password"
                  {...form.register('credentials.webhookSecret')}
                  placeholder="Digite o Webhook Secret"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave secreta para validação de webhooks (opcional)
                </p>
                {form.formState.errors.credentials?.webhookSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.webhookSecret.message?.toString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        );

      case 'zeitbank':
        return (
          <div className="space-y-6">
            {/* Credenciais de PIX */}
            <div className="space-y-4">
              <div className="border-b pb-4">
                <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Credenciais de PIX (Pagamentos)
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Configurações para recebimento de pagamentos via PIX
                </p>
              </div>

              <div>
                <Label htmlFor="pixClientId" className="text-sm font-medium">Client ID (PIX)</Label>
                <Input
                  id="pixClientId"
                  {...form.register('credentials.pixClientId')}
                  placeholder="Ex: 00011561257720847000168"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Client ID fornecido pelo ZeitBank para operações PIX
                </p>
                {form.formState.errors.credentials?.pixClientId && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.pixClientId.message?.toString()}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="pixClientSecret" className="text-sm font-medium">Client Secret (PIX)</Label>
                <Input
                  id="pixClientSecret"
                  type="password"
                  {...form.register('credentials.pixClientSecret')}
                  placeholder="Ex: GE5NzZiZmYtYmU5Ni00Y2M1LTk5Y2ItZ"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Client Secret fornecido pelo ZeitBank para operações PIX
                </p>
                {form.formState.errors.credentials?.pixClientSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.pixClientSecret.message?.toString()}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="pixKey" className="text-sm font-medium">Chave PIX</Label>
                <Input
                  id="pixKey"
                  {...form.register('credentials.pixKey')}
                  placeholder="Ex: acac09e3-08a0-4259-b28d-81eafbeb9065"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave PIX cadastrada no ZeitBank para recebimentos
                </p>
                {form.formState.errors.credentials?.pixKey && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.pixKey.message?.toString()}
                  </p>
                )}
              </div>
            </div>

            {/* Credenciais de Transferência */}
            <div className="space-y-4">
              <div className="border-b pb-4">
                <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Credenciais de Transferência
                </h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Configurações para envio de transferências e pagamentos
                </p>
              </div>

              <div>
                <Label htmlFor="transferClientId" className="text-sm font-medium">Client ID (Transferência)</Label>
                <Input
                  id="transferClientId"
                  {...form.register('credentials.transferClientId')}
                  placeholder="Digite o Client ID para transferências"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Client ID fornecido pelo ZeitBank para operações de transferência
                </p>
                {form.formState.errors.credentials?.transferClientId && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.transferClientId.message?.toString()}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="transferClientSecret" className="text-sm font-medium">Client Secret (Transferência)</Label>
                <Input
                  id="transferClientSecret"
                  type="password"
                  {...form.register('credentials.transferClientSecret')}
                  placeholder="Digite o Client Secret para transferências"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Client Secret fornecido pelo ZeitBank para operações de transferência
                </p>
                {form.formState.errors.credentials?.transferClientSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.transferClientSecret.message?.toString()}
                  </p>
                )}
              </div>
            </div>

            {/* Configurações Gerais */}
            <div className="space-y-4">
              <div className="border-b pb-4">
                <h3 className="text-lg font-medium mb-2">Configurações Gerais</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Configurações compartilhadas entre PIX e transferências
                </p>
              </div>

              <div>
                <Label htmlFor="environment" className="text-sm font-medium">Ambiente</Label>
                <select
                  id="environment"
                  {...form.register('credentials.environment')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                >
                  <option value="">Selecione o ambiente</option>
                  <option value="sandbox">Sandbox (Testes)</option>
                  <option value="production">Produção</option>
                </select>
                <p className="text-xs text-muted-foreground mt-1">
                  Ambiente de operação do ZeitBank
                </p>
                {form.formState.errors.credentials?.environment && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.environment.message?.toString()}
                  </p>
                )}
              </div>
            </div>

            {/* Nota sobre certificados e webhook */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <strong>Nota:</strong> Os certificados SSL e webhook secret são compartilhados entre todas as instâncias do ZeitBank e são configurados via variáveis de ambiente do sistema.
              </AlertDescription>
            </Alert>
          </div>
        );

      case 'xdpag':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="username" className="text-sm font-medium">Username</Label>
                <Input
                  id="username"
                  {...form.register('credentials.username')}
                  placeholder="Digite o username"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Username fornecido pelo XDPAG
                </p>
                {form.formState.errors.credentials?.username && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.username.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="password" className="text-sm font-medium">Password</Label>
                <Input
                  id="password"
                  type="password"
                  {...form.register('credentials.password')}
                  placeholder="Digite a password"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Password fornecida pelo XDPAG
                </p>
                {form.formState.errors.credentials?.password && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.password.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="environment" className="text-sm font-medium">Ambiente</Label>
                <select
                  id="environment"
                  {...form.register('credentials.environment')}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-1"
                >
                  <option value="">Selecione o ambiente</option>
                  <option value="sandbox">Sandbox (Testes)</option>
                  <option value="production">Produção</option>
                </select>
                <p className="text-xs text-muted-foreground mt-1">
                  Ambiente de operação do XDPAG
                </p>
                {form.formState.errors.credentials?.environment && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.environment.message?.toString()}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="webhookSecret" className="text-sm font-medium">Webhook Secret (Opcional)</Label>
                <Input
                  id="webhookSecret"
                  type="password"
                  {...form.register('credentials.webhookSecret')}
                  placeholder="Digite o Webhook Secret"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Chave secreta para validação de webhooks (opcional)
                </p>
                {form.formState.errors.credentials?.webhookSecret && (
                  <p className="text-sm text-red-500 mt-1">
                    {form.formState.errors.credentials.webhookSecret.message?.toString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8">
            <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-sm text-muted-foreground">
              Este gateway não requer configuração de credenciais específicas.
            </p>
            <p className="text-xs text-muted-foreground mt-2">
              As configurações serão aplicadas automaticamente.
            </p>
          </div>
        );
    }
  };

  const currentInstanceNumber = form.watch('instanceNumber');
  const isInstanceConflict = !isConfigured && existingInstances.includes(currentInstanceNumber);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[95vh] overflow-y-auto">
        <DialogHeader className="pb-6">
          <div className="flex items-center gap-4">
            <div className="relative h-14 w-14 overflow-hidden rounded-xl bg-white p-3 flex items-center justify-center border shadow-sm">
              {gateway.logo ? (
                <Image
                  src={gateway.logo}
                  alt={gateway.name}
                  width={48}
                  height={48}
                  className="object-contain"
                />
              ) : (
                <Settings className="h-7 w-7 text-muted-foreground" />
              )}
            </div>
            <div className="flex-1">
              <DialogTitle className="text-2xl font-semibold">
                {isConfigured ? 'Editar Gateway' : 'Configurar Gateway'}
              </DialogTitle>
              <p className="text-muted-foreground mt-1">
                {gateway.name} • {gateway.id.toUpperCase()}
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                {gateway.description}
              </p>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Gateway Info & Credentials Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Configuração do Gateway
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Informações básicas e credenciais de acesso
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Gateway Info Section */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="displayName">Nome de Exibição</Label>
                    <Input
                      id="displayName"
                      {...form.register('displayName')}
                      placeholder="Ex: Pluggou"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Nome personalizado para identificar esta instância
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="description">Descrição</Label>
                    <Input
                      id="description"
                      {...form.register('description')}
                      placeholder="Breve descrição do gateway"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Descrição opcional para documentação
                    </p>
                  </div>
                </div>

                {/* Instance Number Section */}
                <div>
                  <Label htmlFor="instanceNumber">Número da Instância</Label>
                  <Input
                    id="instanceNumber"
                    type="number"
                    min="1"
                    {...form.register('instanceNumber')}
                    placeholder="1"
                    className="mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Número único para identificar esta instância do gateway
                  </p>

                  {/* Mostrar instâncias existentes */}
                  {!isConfigured && existingInstances.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-muted-foreground">
                        Instâncias existentes: {existingInstances.join(', ')}
                      </p>
                    </div>
                  )}

                  {/* Alerta de conflito */}
                  {isInstanceConflict && (
                    <Alert className="mt-2">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        A instância {currentInstanceNumber} já existe. Use uma instância diferente.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>

              <Separator />

              {/* Credentials Section */}
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium flex items-center gap-2 mb-3">
                    <Database className="h-4 w-4" />
                    Credenciais de Acesso
                  </h4>
                  <p className="text-xs text-muted-foreground mb-4">
                    Configure as credenciais necessárias para conectar com o gateway
                  </p>
                </div>
                {renderCredentialFields()}
              </div>
            </CardContent>
          </Card>

          {/* Status & Configurations Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Status e Configurações
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Controle o comportamento e status do gateway
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between p-4 rounded-lg border bg-muted/20 hover:bg-muted/30 transition-colors">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">Gateway Ativo</Label>
                    <p className="text-xs text-muted-foreground">
                      Ativar ou desativar este gateway
                    </p>
                  </div>
                  <Switch
                    checked={form.watch('isActive')}
                    onCheckedChange={(checked) => form.setValue('isActive', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 rounded-lg border bg-muted/20 hover:bg-muted/30 transition-colors">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">Gateway Padrão</Label>
                    <p className="text-xs text-muted-foreground">
                      Definir como gateway padrão do sistema
                    </p>
                  </div>
                  <Switch
                    checked={form.watch('isDefault')}
                    onCheckedChange={(checked) => form.setValue('isDefault', checked)}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="flex items-center justify-between p-4 rounded-lg border bg-muted/20 hover:bg-muted/30 transition-colors">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">Suporte a Recebimento</Label>
                    <p className="text-xs text-muted-foreground">
                      Permitir recebimento de pagamentos
                    </p>
                  </div>
                  <Switch
                    checked={form.watch('canReceive')}
                    onCheckedChange={(checked) => form.setValue('canReceive', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 rounded-lg border bg-muted/20 hover:bg-muted/30 transition-colors">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">Suporte a Envio</Label>
                    <p className="text-xs text-muted-foreground">
                      Permitir envio de pagamentos
                    </p>
                  </div>
                  <Switch
                    checked={form.watch('canSend')}
                    onCheckedChange={(checked) => form.setValue('canSend', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Form Errors */}
          {form.formState.errors.credentials && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">
                {form.formState.errors.credentials.message?.toString()}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-6 border-t bg-muted/20 -mx-6 -mb-6 px-6 py-6">
            <div className="flex items-center space-x-4">
              <div className="text-sm text-muted-foreground">
                {isConfigured ? 'Atualizando configurações do gateway' : 'Configurando novo gateway'}
              </div>
              {isConfigured && gatewayData?.id && (
                <Button
                  type="button"
                  variant="error"
                  onClick={handleDelete}
                  disabled={isDeleting || isSubmitting}
                  size="sm"
                  className="text-xs"
                >
                  {isDeleting ? (
                    <>
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2" />
                      Excluindo...
                    </>
                  ) : (
                    'Excluir Instância'
                  )}
                </Button>
              )}
            </div>
            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting || isDeleting}
                size="lg"
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || isInstanceConflict || isDeleting}
                className="min-w-[140px]"
                size="lg"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Salvando...
                  </>
                ) : (
                  isConfigured ? 'Atualizar Gateway' : 'Configurar Gateway'
                )}
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
