"use client";

import { useState } from 'react';
import Image from 'next/image';
import {
  CheckCircle2,
  <PERSON>Circle,
  Settings,
  AlertCircle,
  ArrowDownToLine,
  ArrowUpFromLine,
  Cog,
  DollarSign
} from 'lucide-react';

import { <PERSON>, CardContent, CardFooter } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Badge } from '@ui/components/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/tooltip';

// Interface for gateway info data
export interface GatewayInfo {
  id: string;
  name: string;
  description: string;
  logo: string;
  features: {
    pixReceive: boolean;
    pixSend: boolean;
  };
  highlight?: boolean;
}

// Interface for configured gateway data
export interface GatewayData {
  id: string;
  isActive: boolean;
  isDefault: boolean;
  priority: number;
  canReceive: boolean;
  canSend: boolean;
  pixChargePercentFee: number;
  pixTransferPercentFee: number;
  pixChargeFixedFee: number;
  pixTransferFixedFee: number;
  credentials?: any;
  displayName?: string;
  description?: string;
  instanceNumber?: number;
  name?: string;
  type?: string;
}

interface GatewayCardWrapperProps {
  gateway: GatewayInfo;
  gatewayData?: GatewayData;
  isConfigured: boolean;
  isActive: boolean;
  organizationId?: string;
}

// Importações do GatewayConfigModal
interface GatewayConfigModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gateway: GatewayInfo;
  gatewayData?: GatewayData;
  isConfigured: boolean;
  onSuccess?: () => void;
}

// Importando dinamicamente para evitar erros de importação circular
import dynamic from 'next/dynamic';
const GatewayConfigModal = dynamic<GatewayConfigModalProps>(
  () => import('./GatewayConfigModal').then((mod) => mod.GatewayConfigModal),
  { ssr: false }
);

// Importando o modal de saldo
interface BalanceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gatewayId: string;
  gatewayName: string;
  organizationId: string;
}

const BalanceModal = dynamic<BalanceModalProps>(
  () => import('./BalanceModal').then((mod) => mod.BalanceModal),
  { ssr: false }
);

// Função para extrair o client ID das credenciais
function getClientIdFromCredentials(credentials: any): string | null {
  if (!credentials) return null;

  // Priorizar clientId, depois apiKey, depois outros identificadores
  return credentials.clientId ||
         credentials.apiKey ||
         credentials.client_id ||
         credentials.api_key ||
         credentials.companyId ||
         credentials.company_id ||
         null;
}

// Função para formatar o client ID para exibição
function formatClientId(clientId: string): string {
  if (!clientId) return 'N/A';
  // Se for muito longo, mostrar só o início e o fim
  if (clientId.length > 16) {
    return `${clientId.substring(0, 8)}...${clientId.substring(clientId.length - 4)}`;
  }
  return clientId;
}

export function GatewayCardWrapper({
  gateway,
  gatewayData,
  isConfigured,
  isActive,
  organizationId = "global",
}: GatewayCardWrapperProps) {
  const [open, setOpen] = useState(false);
  const [balanceOpen, setBalanceOpen] = useState(false);

  const handleSuccess = () => {
    // Refresh page after successful configuration
    window.location.reload();
  };

  // Extrair o client ID das credenciais
  const clientId = gatewayData?.credentials ? getClientIdFromCredentials(gatewayData.credentials) : null;
  const displayClientId = formatClientId(clientId || '');

        // Verificar se o gateway suporta consulta de saldo
  const isOwempayV2 = (
    gateway.id === 'owempay_v2' ||
    gateway.originalType === 'OWEMPAY_V2' ||
    gatewayData?.type === 'OWEMPAY_V2' ||
    gateway.name?.toLowerCase().includes('owempay v2') ||
    gateway.name?.toLowerCase().includes('owempayv2')
  );

  const supportsBalance = isOwempayV2 && isConfigured && isActive;


  return (
    <>
      <Card className={`overflow-hidden ${gateway.highlight ? 'border-primary/50' : ''}`}>
        <CardContent className="p-6">
          <div className="flex items-start justify-between gap-4 mb-4">
            <div className="flex items-center gap-3">
              <div className="relative h-12 w-12 overflow-hidden rounded-md bg-white p-2 flex items-center justify-center">
                {gateway.logo ? (
                  <Image
                    src={gateway.logo}
                    alt={gateway.name}
                    width={50}
                    height={50}
                    className="object-contain"
                  />
                ) : (
                  <Settings className="h-6 w-6 text-muted-foreground" />
                )}
              </div>
              <div>
                <h3 className="font-medium text-lg leading-none">{gateway.name}</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  {clientId ? displayClientId : gateway.id.toUpperCase()}
                </p>
              </div>
            </div>

            {isConfigured &&(
              <Badge className={isActive ? "bg-green-100 text-green-800 hover:bg-green-200" : "bg-gray-100 text-gray-800 hover:bg-gray-200"}>
                {isActive ? "Ativo" : "Inativo"}
              </Badge>
            )}
          </div>

          <p className="text-sm text-muted-foreground mb-4">
            {gateway.description}
          </p>

          <div className="flex flex-wrap gap-2 mb-4">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className={`flex items-center text-xs rounded-full px-2 py-1 ${gateway.features?.pixReceive ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100 text-gray-500'}`}>
                    <ArrowDownToLine className="h-3 w-3 mr-1" />
                    <span>Receber</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  {gateway.features?.pixReceive
                    ? "Este gateway suporta recebimento de pagamentos"
                    : "Este gateway não suporta recebimento de pagamentos"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className={`flex items-center text-xs rounded-full px-2 py-1 ${gateway.features?.pixSend ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-500'}`}>
                    <ArrowUpFromLine className="h-3 w-3 mr-1" />
                    <span>Enviar</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  {gateway.features?.pixSend
                    ? "Este gateway suporta envio de pagamentos"
                    : "Este gateway não suporta envio de pagamentos"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {isConfigured && gatewayData?.isDefault && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center text-xs rounded-full px-2 py-1 bg-amber-100 text-amber-700">
                      <CheckCircle2 className="h-3 w-3 mr-1" />
                      <span>Padrão</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    Este é o gateway padrão do sistema
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>


        </CardContent>

        <CardFooter className="px-6 py-4 bg-muted/20 border-t">
          <div className="flex gap-2 w-full">
            <Button
              onClick={() => setOpen(true)}
              variant={isConfigured ? "outline" : "primary"}
              className="flex-1"
            >
              <Cog className="h-4 w-4 mr-2" />
              {isConfigured ? "Configurar gateway" : "Configurar gateway"}
            </Button>

            {supportsBalance && (
              <Button
                onClick={() => setBalanceOpen(true)}
               variant={"secondary"}

                className="px-3"
                title="Ver Saldo"
              >
                <DollarSign className="h-4 w-4" />
                Ver Saldo
              </Button>
            )}

          </div>
        </CardFooter>
      </Card>

      <GatewayConfigModal
        open={open}
        onOpenChange={setOpen}
        gateway={gateway}
        gatewayData={gatewayData}
        isConfigured={isConfigured}
        onSuccess={handleSuccess}
      />

      {supportsBalance && (
        <BalanceModal
          open={balanceOpen}
          onOpenChange={setBalanceOpen}
          gatewayId={gatewayData?.id || gateway.id}
          gatewayName={gateway.name}
          organizationId={organizationId}
        />
      )}
    </>
  );
}

