import { PageHeader } from "@saas/shared/components/PageHeader";
import { Card, CardContent } from "@ui/components/card";
import { db } from "@repo/database";
import { Button } from "@ui/components/button";
import { PlusIcon, Info, AlertCircle, Layers } from "lucide-react";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Alert, AlertDescription, AlertTitle } from "@ui/components/alert";
import { GatewayCardWrapper } from "./components/GatewayCardWrapper";
import { Badge } from "@ui/components/badge";

export const metadata = {
  title: "Gateways de Pagamento",
  description: "Administre todos os gateways de pagamento disponíveis no sistema",
};

// Available gateway types with display info
const availableGateways = [


  {
    id: "reflowpay",
    name: "ReflowPay",
    description: "Processador de pagamentos com foco em Pix e métodos alternativos.",
    logo: "/images/gateways/reflowpay.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },

  {
    id: "pixium",
    name: "Pixi<PERSON>",
    description: "Plataforma especializada em pagamentos Pix para empresas de todos os portes.",
    logo: "/images/gateways/pixium.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "transfeera",
    name: "Transfeera",
    description: "Solução completa para transferências e pagamentos via Pix.",
    logo: "/images/gateways/transfeera.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: false
  },
  {
    id: "mediuspag",
    name: "MediusPag",
    description: "Plataforma de pagamentos com suporte a Pix e outros métodos.",
    logo: "/images/gateways/mediuspag.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "ecomovi",
    name: "Ecomovi",
    description: "Gateway de pagamentos Pix da Onz Finance com suporte completo a PIX IN e PIX OUT.",
    logo: "/images/gateways/ecomovi.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: false
  },
  {
    id: "zendry",
    name: "Zendry",
    description: "Plataforma de pagamentos PIX com QR codes dinâmicos e webhooks em tempo real.",
    logo: "/images/gateways/zendry.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: true
  },
  {
    id: "cartwave",
    name: "Cartwave Hub",
    description: "Gateway de pagamentos com suporte completo a PIX IN e PIX OUT para recebimento e envio de pagamentos.",
    logo: "/images/gateways/cartwave.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: true
  },
  {
    id: "pagarme",
    name: "Pagar.me",
    description: "Plataforma completa de pagamentos com suporte a Pix e diversos métodos de pagamento.",
    logo: "/images/gateways/pagarme.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: false
  },
  {
    id: "owempay",
    name: "Owempay",
    description: "Plataforma de pagamentos PIX com suporte completo a recebimentos, transferências e reembolsos.",
    logo: "/images/gateways/owempay.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: true
  },
  {
    id: "owempay_v2",
    name: "Owempay v2",
    description: "Nova versão da API Owempay com autenticação Basic Auth e endpoints otimizados para PIX.",
    logo: "/images/gateways/owempay.png",
    features: {
      pixReceive: true,
      pixSend: false
    },
    highlight: true
  },
  {
    id: "zeitbank",
    name: "ZeitBank",
    description: "Gateway de pagamentos PIX da ZeitBank com suporte completo a PIX IN e PIX OUT.",
    logo: "/images/gateways/zeitbank.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: true
  },
  {
    id: "xdpag",
    name: "XDPAG",
    description: "Gateway de pagamentos PIX da XDPAG com suporte completo a PIX IN e PIX OUT.",
    logo: "/images/gateways/xdpag.png",
    features: {
      pixReceive: true,
      pixSend: true
    },
    highlight: true
  }
];

// Definir tipo para gateway com todas as propriedades esperadas
interface PaymentGatewayWithFees {
  id: string;
  type: string;
  name: string;
  displayName?: string | null;
  isActive: boolean;
  isDefault: boolean;
  priority: number;
  credentials: any;
  canReceive: boolean;
  canSend: boolean;
  createdAt: Date;
  updatedAt: Date;
  configuredById: string | null;
  instanceNumber: number;
  description?: string | null;
}

export default async function AdminGatewaysPage() {
  let configuredGateways: any[] = [];

  try {
    // Get all gateways from database
    configuredGateways = await db.payment_gateway.findMany({
      orderBy: [
        { type: 'asc' },
        { instanceNumber: 'asc' },
        { priority: 'asc' },
      ],
      select: {
        id: true,
        type: true,
        name: true,
        displayName: true,
        credentials: true,
        isActive: true,
        isDefault: true,
        priority: true,
        canReceive: true,
        canSend: true,
        configuredById: true,
        createdAt: true,
        updatedAt: true,
        instanceNumber: true,
        description: true,
      }
    });
  } catch (dbError) {
    console.error("Erro ao conectar com o banco de dados:", dbError);
    // Continue com array vazio se houver erro de conexão
    configuredGateways = [];
  }

  // Map the configured gateways for easier lookup - now supporting multiple instances
  const configuredGatewayMap: Record<string, any[]> = {};
  const configuredGatewayInstances: PaymentGatewayWithFees[] = [];

  configuredGateways.forEach((gateway: PaymentGatewayWithFees) => {
    const gatewayKey = gateway.type.toLowerCase();

    if (!configuredGatewayMap[gatewayKey]) {
      configuredGatewayMap[gatewayKey] = [];
    }

    const gatewayData = {
      id: gateway.id,
      isActive: gateway.isActive,
      isDefault: gateway.isDefault,
      priority: gateway.priority || 999,
      canReceive: gateway.canReceive ?? true,
      canSend: gateway.canSend ?? false,
      pixChargePercentFee: 0,
      pixTransferPercentFee: 0,
      pixChargeFixedFee: 0,
      pixTransferFixedFee: 0,
      credentials: gateway.credentials,
      instanceNumber: gateway.instanceNumber,
      displayName: gateway.displayName || undefined,
      description: gateway.description,
      name: gateway.name,
      type: gateway.type,
    };

    configuredGatewayMap[gatewayKey].push(gatewayData);
    configuredGatewayInstances.push(gateway);
  });

  // Categorize gateways based on configuration status - now with multiple instances support
  const activeGatewayInstances = configuredGatewayInstances.filter(gateway => gateway.isActive);
  const inactiveGatewayInstances = configuredGatewayInstances.filter(gateway => !gateway.isActive);

  // Get gateway types that have at least one configured instance
  const configuredGatewayTypes = new Set(configuredGatewayInstances.map(g => g.type.toLowerCase()));
  const unconfiguredGateways = availableGateways.filter(gateway =>
    !configuredGatewayTypes.has(gateway.id.toLowerCase())
  );

  // Group gateways by type for better organization
  const gatewayGroups = configuredGatewayInstances.reduce((groups, gateway) => {
    const type = gateway.type.toLowerCase();
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(gateway);
    return groups;
  }, {} as Record<string, PaymentGatewayWithFees[]>);

  // Create enhanced gateway objects for display
  const createGatewayDisplayObject = (gatewayInstance: PaymentGatewayWithFees) => {
    const baseGateway = availableGateways.find(g => g.id.toLowerCase() === gatewayInstance.type.toLowerCase());

    // Use existing displayName or generate one based on name and instance number
    const displayName = gatewayInstance.displayName ||
      (gatewayInstance.instanceNumber > 1
        ? `${baseGateway?.name} #${gatewayInstance.instanceNumber}`
        : baseGateway?.name || gatewayInstance.name);

    return {
      ...(baseGateway || {
        id: gatewayInstance.type.toLowerCase(),
        name: gatewayInstance.name,
        description: "Gateway de pagamento",
        logo: "/images/gateways/default.png",
        features: {
          pixReceive: gatewayInstance.canReceive ?? true,
          pixSend: gatewayInstance.canSend ?? false
        },
        highlight: false
      }),
      id: gatewayInstance.id, // Use the instance ID
      name: displayName,
      description: gatewayInstance.description || baseGateway?.description || "Gateway de pagamento",
      instanceNumber: gatewayInstance.instanceNumber,
      originalType: gatewayInstance.type,
    };
  };

  const activeGateways = activeGatewayInstances.map(createGatewayDisplayObject);
  const inactiveGateways = inactiveGatewayInstances.map(createGatewayDisplayObject);


  // Check if there's at least one default gateway
  const hasDefaultGateway = configuredGatewayInstances.some(
     (gateway: PaymentGatewayWithFees) => gateway.isDefault && gateway.isActive
   );
   const activeGatewaysCount = configuredGatewayInstances.filter(gateway => gateway.isActive).length;

  // Function to map gateway data for display - updated for multiple instances
  const mapGatewayData = (gateway: any): any => {
    // For configured instances, find the specific instance data
    if (gateway.id && gateway.id.length > 10) { // This is an instance ID
      const instanceData = configuredGatewayInstances.find(g => g.id === gateway.id);
      if (instanceData) {
        return {
          status: instanceData.isActive ? 'active' : 'inactive',
          priority: instanceData.priority,
          canReceive: instanceData.canReceive,
          canSend: instanceData.canSend,
          pixChargePercentFee: 0,
          pixTransferPercentFee: 0,
          pixChargeFixedFee: 0,
          pixTransferFixedFee: 0,
          credentials: instanceData.credentials,
          isDefault: instanceData.isDefault,
          id: instanceData.id,
        };
      }
    }

    // For unconfigured gateways
    return {
      status: 'not_configured',
      priority: 999,
      canReceive: true,
      canSend: false,
      pixChargePercentFee: 0,
      pixTransferPercentFee: 0,
      pixChargeFixedFee: 0,
      pixTransferFixedFee: 0,
      credentials: {},
    };
  };

  return (
      <div className="space-y-6">
        <PageHeader
          title="Gateways de Pagamento"
          subtitle="Administre todos os gateways de pagamento disponíveis no sistema"
        />

        {!hasDefaultGateway && activeGatewaysCount > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Nenhum gateway padrão definido</AlertTitle>
            <AlertDescription>
              Existem gateways ativos, mas nenhum está definido como padrão. É recomendável definir pelo menos um gateway padrão.
            </AlertDescription>
          </Alert>
        )}

        {activeGatewaysCount === 0 && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Nenhum gateway ativo</AlertTitle>
            <AlertDescription>
              Não há gateways ativos no sistema. Configure e ative pelo menos um gateway para permitir transações.
            </AlertDescription>
          </Alert>
        )}



        <Tabs defaultValue="all" className="w-full">
          <div className="flex items-center justify-between mb-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">Todos</TabsTrigger>
              <TabsTrigger value="active">Ativos</TabsTrigger>
              <TabsTrigger value="inactive">Inativos</TabsTrigger>
              <TabsTrigger value="available">Disponíveis</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="all" className="space-y-6">
            {activeGateways.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gateways Ativos</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {activeGateways.map((gateway) => {
                    // Encontrar os dados específicos desta instância
                    const instanceData = configuredGatewayInstances.find(g => g.id === gateway.id);
                    const gatewayData = instanceData ? {
                      id: instanceData.id,
                      isActive: instanceData.isActive,
                      isDefault: instanceData.isDefault,
                      priority: instanceData.priority || 999,
                      canReceive: instanceData.canReceive ?? true,
                      canSend: instanceData.canSend ?? false,
                      pixChargePercentFee: 0,
                      pixTransferPercentFee: 0,
                      pixChargeFixedFee: 0,
                      pixTransferFixedFee: 0,
                      credentials: instanceData.credentials,
                      instanceNumber: instanceData.instanceNumber,
                      displayName: instanceData.displayName || undefined,
                      description: instanceData.description || undefined,
                      name: instanceData.name,
                      type: instanceData.type,
                    } : undefined;

                    return (
                      <GatewayCardWrapper
                        key={gateway.id}
                        gateway={gateway}
                        gatewayData={gatewayData}
                        isConfigured={true}
                        isActive={true}
                        organizationId="global"
                      />
                    );
                  })}
                </div>
              </div>
            )}

            {inactiveGateways.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gateways Inativos</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {inactiveGateways.map((gateway) => {
                    // Encontrar os dados específicos desta instância
                    const instanceData = configuredGatewayInstances.find(g => g.id === gateway.id);
                    const gatewayData = instanceData ? {
                      id: instanceData.id,
                      isActive: instanceData.isActive,
                      isDefault: instanceData.isDefault,
                      priority: instanceData.priority || 999,
                      canReceive: instanceData.canReceive ?? true,
                      canSend: instanceData.canSend ?? false,
                      pixChargePercentFee: 0,
                      pixTransferPercentFee: 0,
                      pixChargeFixedFee: 0,
                      pixTransferFixedFee: 0,
                      credentials: instanceData.credentials,
                      instanceNumber: instanceData.instanceNumber,
                      displayName: instanceData.displayName || undefined,
                      description: instanceData.description || undefined,
                      name: instanceData.name,
                      type: instanceData.type,
                    } : undefined;

                    return (
                      <GatewayCardWrapper
                        key={gateway.id}
                        gateway={gateway}
                        gatewayData={gatewayData}
                        isConfigured={true}
                        isActive={false}
                        organizationId="global"
                      />
                    );
                  })}
                </div>
              </div>
            )}

            {unconfiguredGateways.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Gateways Disponíveis</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {unconfiguredGateways.map((gateway) => (
                    <GatewayCardWrapper
                      key={gateway.id}
                      gateway={gateway}
                      isConfigured={false}
                      isActive={false}
                      organizationId="global"
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Section for creating new instances of configured gateways */}
            {configuredGatewayTypes.size > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Criar Nova Instância</h3>
                  <p className="text-sm text-muted-foreground">
                    Crie novas instâncias de gateways já configurados com diferentes credenciais
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {availableGateways
                    .filter(gateway => configuredGatewayTypes.has(gateway.id.toLowerCase()))
                    .map((gateway) => (
                      <GatewayCardWrapper
                        key={`new-instance-${gateway.id}`}
                        gateway={{
                          ...gateway,
                          name: `${gateway.name} - Nova Instância`,
                          description: `Criar uma nova instância do ${gateway.name} com credenciais diferentes`
                        }}
                        isConfigured={false}
                        isActive={false}
                        organizationId="global"
                      />
                    ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Gateways Ativos</h3>
            </div>
            {activeGateways.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {activeGateways.map((gateway) => {
                  // Encontrar os dados específicos desta instância
                  const instanceData = configuredGatewayInstances.find(g => g.id === gateway.id);
                  const gatewayData = instanceData ? {
                    id: instanceData.id,
                    isActive: instanceData.isActive,
                    isDefault: instanceData.isDefault,
                    priority: instanceData.priority || 999,
                    canReceive: instanceData.canReceive ?? true,
                    canSend: instanceData.canSend ?? false,
                    pixChargePercentFee: 0,
                    pixTransferPercentFee: 0,
                    pixChargeFixedFee: 0,
                    pixTransferFixedFee: 0,
                    credentials: instanceData.credentials,
                    instanceNumber: instanceData.instanceNumber,
                    displayName: instanceData.displayName || undefined,
                    description: instanceData.description || undefined,
                    name: instanceData.name,
                    type: instanceData.type,
                  } : undefined;

                  return (
                    <GatewayCardWrapper
                      key={gateway.id}
                      gateway={gateway}
                      gatewayData={gatewayData}
                      isConfigured={true}
                      isActive={true}
                      organizationId="global"
                    />
                  );
                })}
              </div>
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-muted-foreground">
                    Nenhum gateway ativo configurado.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="inactive" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Gateways Inativos</h3>
            </div>
            {inactiveGateways.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {inactiveGateways.map((gateway) => {
                  // Encontrar os dados específicos desta instância
                  const instanceData = configuredGatewayInstances.find(g => g.id === gateway.id);
                  const gatewayData = instanceData ? {
                    id: instanceData.id,
                    isActive: instanceData.isActive,
                    isDefault: instanceData.isDefault,
                    priority: instanceData.priority || 999,
                    canReceive: instanceData.canReceive ?? true,
                    canSend: instanceData.canSend ?? false,
                    pixChargePercentFee: 0,
                    pixTransferPercentFee: 0,
                    pixChargeFixedFee: 0,
                    pixTransferFixedFee: 0,
                    credentials: instanceData.credentials,
                    instanceNumber: instanceData.instanceNumber,
                    displayName: instanceData.displayName || undefined,
                    description: instanceData.description || undefined,
                    name: instanceData.name,
                    type: instanceData.type,
                  } : undefined;

                  return (
                    <GatewayCardWrapper
                      key={gateway.id}
                      gateway={gateway}
                      gatewayData={gatewayData}
                      isConfigured={true}
                      isActive={false}
                      organizationId="global"
                    />
                  );
                })}
              </div>
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-center text-muted-foreground">
                    Nenhum gateway inativo configurado.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="available" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Gateways Disponíveis</h3>
            </div>
            <div className="space-y-6">
              {unconfiguredGateways.length > 0 && (
                <div className="space-y-4">
                  <h4 className="text-md font-medium">Gateways Não Configurados</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {unconfiguredGateways.map((gateway) => (
                      <GatewayCardWrapper
                        key={gateway.id}
                        gateway={gateway}
                        isConfigured={false}
                        isActive={false}
                        organizationId="global"
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Section for creating new instances of configured gateways */}
              {configuredGatewayTypes.size > 0 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="text-md font-medium">Criar Nova Instância</h4>
                    <p className="text-sm text-muted-foreground">
                      Crie novas instâncias de gateways já configurados com diferentes credenciais
                    </p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {availableGateways
                      .filter(gateway => configuredGatewayTypes.has(gateway.id.toLowerCase()))
                      .map((gateway) => (
                        <GatewayCardWrapper
                          key={`new-instance-${gateway.id}`}
                          gateway={{
                            ...gateway,
                            name: `${gateway.name} - Nova Instância`,
                            description: `Criar uma nova instância do ${gateway.name} com credenciais diferentes`
                          }}
                          isConfigured={false}
                          isActive={false}
                          organizationId="global"
                        />
                      ))}
                  </div>
                </div>
              )}

              {unconfiguredGateways.length === 0 && configuredGatewayTypes.size === 0 && (
                <Card>
                  <CardContent className="p-6 text-center">
                    <p className="text-muted-foreground">Todos os gateways disponíveis já foram configurados.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    );
}
