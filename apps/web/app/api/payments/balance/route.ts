import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";

/**
 * Endpoint para obter o saldo atual da organização
 * GET /api/payments/balance?organizationId=xyz
 */
export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getSession();
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Obter ID da organização da query
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');

    if (!organizationId) {
      return NextResponse.json({ error: "Organization ID is required" }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findUnique({
      where: {
        userId_organizationId: {
          userId: session.user.id,
          organizationId,
        },
      },
    });

    if (!membership) {
      logger.warn("Unauthorized access attempt to organization balance", {
        userId: session.user.id,
        organizationId
      });
      return NextResponse.json({ error: "Unauthorized for this organization" }, { status: 403 });
    }

    // Verificar se a organização está com status APPROVED
    const organization = await db.organization.findUnique({
      where: { id: organizationId },
      select: { status: true }
    });

    if (!organization) {
      return NextResponse.json({ error: "Organization not found" }, { status: 404 });
    }

    if (organization.status !== "APPROVED") {
      return NextResponse.json({
        error: "Organization is not approved",
        status: organization.status
      }, { status: 403 });
    }

    // Buscar o saldo atual da organização no banco de dados
    let balance = await db.organization_balance.findUnique({
      where: { organizationId }
    });

    // Se não existir registro de saldo, criar um com valores zerados
    if (!balance) {
      balance = await db.organization_balance.create({
        data: {
          organizationId,
          availableBalance: 0,
          pendingBalance: 0,
          reservedBalance: 0,
        }
      });

      logger.info("Created initial balance record for organization", { organizationId });
    }

    // Retornar dados de saldo em formato amigável
    return NextResponse.json({
      success: true,
      available: balance.availableBalance,
      pending: balance.pendingBalance,
      reserved: balance.reservedBalance,
      updatedAt: balance.updatedAt
    });
  } catch (error) {
    logger.error("Error fetching balance:", error);
    return NextResponse.json({
      error: "Internal Server Error",
      message: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

/**
 * Verificar se uma organização tem saldo disponível suficiente para uma transação
 * Função utilitária que pode ser usada em outras partes do sistema
 */
export async function hasAvailableBalance(organizationId: string, amount: number): Promise<boolean> {
  try {
    const balance = await db.organization_balance.findUnique({
      where: { organizationId }
    });

    if (!balance) {
      logger.warn("Balance record not found for organization", { organizationId });
      return false;
    }

    return balance.availableBalance >= amount;
  } catch (error) {
    logger.error("Error checking available balance", { error, organizationId, amount });
    return false;
  }
}
