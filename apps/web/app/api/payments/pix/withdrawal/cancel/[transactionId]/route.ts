import { NextRequest, NextResponse } from "next/server";
import { logger } from "@repo/logs";
import { db } from "@repo/database";
import { z } from "zod";

// Schema de validação para cancelamento
const CancelWithdrawalSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  reason: z.string().optional()
});

interface RouteParams {
  params: {
    transactionId: string;
  };
}

// Endpoint para cancelar PIX withdrawal
export async function POST(
  req: NextRequest,
  { params }: RouteParams
) {
  try {
    const { transactionId } = params;
    
    logger.info("PIX withdrawal cancel endpoint called", { transactionId });

    // Parse do corpo da requisição
    const body = await req.json();
    
    // Validar dados de entrada
    const validationResult = CancelWithdrawalSchema.safeParse(body);
    
    if (!validationResult.success) {
      logger.error("Invalid cancel withdrawal request data", {
        errors: validationResult.error.errors,
        transactionId
      });
      
      return NextResponse.json(
        {
          success: false,
          error: "Invalid request data",
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { organizationId, reason } = validationResult.data;

    // Verificar se a transação existe e pertence à organização
    const transaction = await db.transaction.findFirst({
      where: {
        AND: [
          {
            OR: [
              { id: transactionId },
              { externalId: transactionId }
            ]
          },
          { organizationId },
          { type: "WITHDRAWAL" }
        ]
      },
      select: {
        id: true,
        externalId: true,
        status: true,
        amount: true,
        createdAt: true,
        metadata: true,
        organizationId: true
      }
    });

    if (!transaction) {
      logger.error("Withdrawal transaction not found", {
        transactionId,
        organizationId
      });
      
      return NextResponse.json(
        {
          success: false,
          error: "Withdrawal transaction not found"
        },
        { status: 404 }
      );
    }

    // Verificar se a transação pode ser cancelada
    const cancellableStatuses = ["PENDING", "PROCESSING"];
    
    if (!cancellableStatuses.includes(transaction.status)) {
      logger.error("Transaction cannot be cancelled", {
        transactionId: transaction.id,
        currentStatus: transaction.status,
        cancellableStatuses
      });
      
      return NextResponse.json(
        {
          success: false,
          error: `Transaction with status '${transaction.status}' cannot be cancelled`,
          currentStatus: transaction.status,
          cancellableStatuses
        },
        { status: 400 }
      );
    }

    // Atualizar status da transação para CANCELLED
    const updatedTransaction = await db.transaction.update({
      where: { id: transaction.id },
      data: {
        status: "CANCELLED",
        updatedAt: new Date(),
        metadata: {
          ...transaction.metadata,
          cancellation: {
            cancelledAt: new Date().toISOString(),
            reason: reason || "Cancelled by user",
            cancelledBy: "API"
          }
        }
      },
      select: {
        id: true,
        externalId: true,
        status: true,
        amount: true,
        createdAt: true,
        updatedAt: true,
        metadata: true
      }
    });

    // Se a organização tem saldo, devolver o valor
    try {
      const balance = await db.balance.findUnique({
        where: { organizationId }
      });

      if (balance) {
        await db.balance.update({
          where: { organizationId },
          data: {
            available: {
              increment: transaction.amount
            },
            updatedAt: new Date()
          }
        });

        // Registrar movimento de estorno
        await db.balanceMovement.create({
          data: {
            organizationId,
            type: "CREDIT",
            amount: transaction.amount,
            description: `Estorno de saque PIX cancelado - ${transaction.id}`,
            transactionId: transaction.id,
            metadata: {
              type: "WITHDRAWAL_CANCELLATION",
              originalTransactionId: transaction.id,
              reason: reason || "Cancelled by user"
            }
          }
        });

        logger.info("Balance restored after withdrawal cancellation", {
          organizationId,
          transactionId: transaction.id,
          restoredAmount: transaction.amount
        });
      }
    } catch (balanceError) {
      logger.error("Error restoring balance after withdrawal cancellation", {
        error: balanceError instanceof Error ? balanceError.message : balanceError,
        transactionId: transaction.id,
        organizationId
      });
      // Continue even if balance restoration fails
    }

    // Extrair informações relevantes do metadata
    const metadata = updatedTransaction.metadata as any;
    const ecomoviData = metadata?.ecomovi || {};
    const cancellationData = metadata?.cancellation || {};

    logger.info("PIX withdrawal cancelled successfully", {
      transactionId: updatedTransaction.id,
      previousStatus: transaction.status,
      newStatus: updatedTransaction.status,
      reason: cancellationData.reason
    });

    return NextResponse.json({
      success: true,
      data: {
        transactionId: updatedTransaction.id,
        externalId: updatedTransaction.externalId,
        status: updatedTransaction.status,
        amount: updatedTransaction.amount,
        createdAt: updatedTransaction.createdAt,
        updatedAt: updatedTransaction.updatedAt,
        pixKey: ecomoviData.pixKey,
        pixKeyType: ecomoviData.pixKeyType,
        cancellation: {
          cancelledAt: cancellationData.cancelledAt,
          reason: cancellationData.reason,
          cancelledBy: cancellationData.cancelledBy
        }
      }
    });

  } catch (error) {
    logger.error("Error cancelling PIX withdrawal", {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      transactionId: params.transactionId
    });

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error while cancelling withdrawal"
      },
      { status: 500 }
    );
  }
}