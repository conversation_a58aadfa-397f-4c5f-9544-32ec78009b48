import { app } from "@repo/api";
import { handle } from "hono/vercel";
import { NextRequest, NextResponse } from "next/server";
import { db } from "@repo/database";
import { createHash } from "crypto";
import { auth, getOrganizationMembership } from "@repo/auth";
import { headers } from "next/headers";
import { logger } from "@repo/logs";

// Use the handler for default behavior, but implement custom handler for GET
const honoHandler = handle(app);

// Custom API key authentication
async function validateApiKey(req: NextRequest) {
  // Get API key from header
  const apiKey = req.headers.get("X-API-Key");

  if (!apiKey) {
    return null;
  }

  try {
    // Calculate hash of the API key
    const hash = createHash("sha256").update(apiKey).digest("hex");

    // Find API key by hash
    const key = await db.api_key.findFirst({
      where: { hash },
      include: {
        organization: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!key) {
      return null;
    }

    // Check if API key has expired (expiresAt is optional)
    // Access expiresAt using bracket notation to avoid TypeScript errors
    const expiresAt = (key as any).expiresAt as Date | null | undefined;
    if (expiresAt && expiresAt < new Date()) {
      return null;
    }

    // Update lastUsedAt
    try {
      await db.api_key.update({
        where: { id: key.id },
        data: { lastUsedAt: new Date() }
      });
    } catch (error) {
      // Continue even if update fails
      console.warn("Failed to update lastUsedAt", error);
    }

    return {
      apiKey: key,
      organization: key.organization,
      user: key.createdBy
    };
  } catch (error) {
    console.error("Error validating API key", error);
    return null;
  }
}

// Custom handler for GET transaction by ID
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = params;

    // Validate API key
    const auth = await validateApiKey(req);

    if (!auth) {
      // Fall back to Hono handler which will handle session auth
      return honoHandler(req);
    }

    // Get the transaction
    const transaction = await db.transaction.findUnique({
      where: { id },
      include: {
        payment_gateway: true,
        refunds: true,
        blocks: true,
      },
    }) as any; // Use 'any' type to avoid TypeScript errors during migration

    if (!transaction) {
      return NextResponse.json({ error: "Transaction not found" }, { status: 404 });
    }

    // Check if the API key has access to this transaction's organization
    if (auth.organization.id !== transaction.organizationId) {
      return NextResponse.json(
        { error: "You don't have access to this transaction" },
        { status: 403 }
      );
    }

    // Extract PIX information from transaction metadata
    const metadata = transaction.metadata as Record<string, any> || {};

    // Get endToEndId from the dedicated column, with fallback to metadata for backward compatibility
    const endToEndId = transaction.endToEndId ||
                       metadata?.endToEndId ||
                       metadata?.pixEndToEndId ||
                       metadata?.transferDetails?.endToEndId ||
                       metadata?.paymentDetails?.endToEndId;

    return NextResponse.json({
      id: transaction.id,
      externalId: transaction.externalId,
      referenceCode: transaction.referenceCode,
      customerName: transaction.customerName,
      customerEmail: transaction.customerEmail,
      customerPhone: transaction.customerPhone,
      customerDocument: transaction.customerDocument,
      customerDocumentType: transaction.customerDocumentType,
      amount: transaction.amount,
      status: transaction.status,
      type: transaction.type,
      description: transaction.description,
      createdAt: transaction.createdAt,
      paymentAt: transaction.paymentAt,
      updatedAt: transaction.updatedAt,
      // Include PIX specific identifiers
      endToEndId: endToEndId,
      pixEndToEndId: endToEndId,
      pix: {
        qrCode: metadata.pixCode,
        qrCodeImage: metadata.pixQrCode,
        expirationDate: metadata.pixExpiresAt,
        txid: metadata.txid,
        endToEndId: endToEndId
      },
      refunds: transaction.refunds.map((refund: any) => ({
        id: refund.id,
        amount: refund.amount,
        status: refund.status,
        reason: refund.reason,
        createdAt: refund.createdAt,
        processedAt: refund.processedAt,
      })),
      blocks: transaction.blocks.map((block: any) => ({
        id: block.id,
        reason: block.reason,
        status: block.status,
        createdAt: block.createdAt,
        releasedAt: block.releasedAt,
      })),
    });
  } catch (error) {
    console.error("Error getting transaction", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Use Hono handler for other methods
export const POST = honoHandler;
export const PUT = honoHandler;
export const PATCH = honoHandler;
export const DELETE = honoHandler;
export const OPTIONS = honoHandler;
