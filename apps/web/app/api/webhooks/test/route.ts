import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { headers } from "next/headers";
import { db } from "@repo/database";
import { WebhookEventType } from "@repo/payments/src/webhooks/events";
import { logger } from "@repo/logs";
import { svixService, EVENT_TYPE_MAPPING } from "@repo/utils";
import { getSvixConfig } from "@repo/payments/src/webhooks/svix-service";
import { createHash } from "crypto";

/**
 * Test webhook API that sends realistic PIX events through SVIX
 * This endpoint sends test events to organization-specific channels
 */
export async function POST(req: NextRequest) {
  try {
    // Verify authentication (session or API key)
    const headersList = await headers();
    const apiKey = req.headers.get("X-API-Key");

    let userId: string | null = null;
    let authenticatedOrgId: string | null = null;

    if (apiKey) {
      // API key authentication
      const hash = createHash("sha256").update(apiKey).digest("hex");

      const key = await db.api_key.findFirst({
        where: { hash },
        include: {
          organization: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      });

      if (!key) {
        return NextResponse.json(
          { error: "Invalid API key" },
          { status: 401 }
        );
      }

      // Check if API key has expired
      const expiresAt = (key as any).expiresAt as Date | null | undefined;
      if (expiresAt && expiresAt < new Date()) {
        return NextResponse.json(
          { error: "API key has expired" },
          { status: 401 }
        );
      }

      // Check if user exists
      if (!key.user) {
        return NextResponse.json(
          { error: "Invalid API key configuration" },
          { status: 401 }
        );
      }

      userId = key.createdById;
      authenticatedOrgId = key.organizationId;

      // Update lastUsedAt
      try {
        await db.api_key.update({
          where: { id: key.id },
          data: { lastUsedAt: new Date() }
        });
      } catch (updateError) {
        console.warn("Failed to update lastUsedAt for API key:", updateError);
      }
    } else {
      // Session authentication
      const session = await auth.api.getSession({
        headers: headersList,
      });
      if (!session?.user?.id) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        );
      }
      userId = session.user.id;
    }

    // Get request body
    const body = await req.json();
    const { eventType, organizationId } = body;

    if (!eventType) {
      return NextResponse.json(
        { error: "eventType é obrigatório" },
        { status: 400 }
      );
    }

    if (!organizationId) {
      return NextResponse.json(
        { error: "organizationId é obrigatório" },
        { status: 400 }
      );
    }

    logger.info("Testing webhook with SVIX", {
      eventType,
      organizationId,
      userId: userId
    });

    // Validate that the event type is a PIX event
    const validPixEvents = [
      WebhookEventType.PIX_IN_PROCESSING,
      WebhookEventType.PIX_IN_CONFIRMATION,
      WebhookEventType.PIX_OUT_PROCESSING,
      WebhookEventType.PIX_OUT_CONFIRMATION,
      WebhookEventType.PIX_OUT_FAILURE,
      WebhookEventType.PIX_IN_REVERSAL_PROCESSING,
      WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION,
      WebhookEventType.PIX_OUT_REVERSAL,
    ];

    if (!validPixEvents.includes(eventType as WebhookEventType)) {
      return NextResponse.json(
        { error: `Tipo de evento inválido. Use um dos seguintes: ${validPixEvents.join(', ')}` },
        { status: 400 }
      );
    }

    // Verify organization exists
    const organization = await db.organization.findFirst({
      where: {
        OR: [
          { id: organizationId },
          { slug: organizationId }
        ]
      },
      select: { id: true, name: true }
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organização não encontrada" },
        { status: 404 }
      );
    }

    // Get SVIX configuration
    const svixConfig = await getSvixConfig();

    // Generate realistic test payload based on event type
    const testPayload = generateRealisticPayload(eventType as WebhookEventType, organization.id);

    logger.info("Generated test payload", {
      eventType,
      organizationId: organization.id,
      payloadKeys: Object.keys(testPayload)
    });

    // Map internal event type to SVIX format
    const svixEventType = EVENT_TYPE_MAPPING[eventType as keyof typeof EVENT_TYPE_MAPPING] || eventType;

    // Send event through SVIX to organization-specific channel using standardized format
    const messageId = await svixService.sendEvent(
      svixConfig.appId,
      svixEventType,
      testPayload, // testPayload already has the correct format
      organization.id
    );

    // Create webhook event record in database for tracking
    const webhookEvent = await db.webhook_event.create({
      data: {
        type: eventType,
        payload: testPayload.data, // Store only the transaction data, not the wrapper
        organizationId: organization.id,
        svixMessageId: messageId,
        svixEventType: svixEventType,
      },
    });

    logger.info("Test webhook event sent successfully", {
      eventType,
      svixEventType,
      organizationId: organization.id,
      messageId,
      webhookEventId: webhookEvent.id,
    });

    return NextResponse.json({
      success: true,
      message: `Evento de teste ${eventType} enviado com sucesso via SVIX`,
      data: {
        eventType,
        svixEventType,
        organizationId: organization.id,
        organizationName: organization.name,
        messageId,
        webhookEventId: webhookEvent.id,
        channel: `org-${organization.id}`,
        payload: testPayload
      }
    });

  } catch (error) {
    const errorInfo = {
      message: error instanceof Error ? error.message : String(error || "Unknown error"),
      name: error instanceof Error ? error.name : "UnknownError",
      stack: error instanceof Error ? error.stack : undefined
    };

    logger.error("Error testing webhook", errorInfo);
    return NextResponse.json(
      {
        error: "Falha ao testar webhook",
        details: errorInfo.message
      },
      { status: 500 }
    );
  }
}

/**
 * Generate realistic test payload based on event type
 * This matches the exact structure used in production webhook events
 */
function generateRealisticPayload(eventType: WebhookEventType, organizationId: string) {
  const now = new Date();
  const baseTimestamp = now.toISOString();
  const webhookEventId = `cmb${Date.now().toString(36)}${Math.random().toString(36).substring(2, 8)}`;
  const transactionId = `cmb${Date.now().toString(36)}${Math.random().toString(36).substring(2, 8)}`;

  // Generate realistic endToEndId following the pattern E + bank code + date + sequence
  const bankCode = "********"; // Pluggou bank code
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '').slice(2); // YYMMDD
  const timeStr = now.toISOString().slice(11, 19).replace(/:/g, '').slice(0, 4); // HHMM
  const sequence = Math.random().toString(36).substring(2, 15);
  const endToEndId = `E${bankCode}${dateStr}${timeStr}${sequence}`;

  // Generate realistic amounts in cents (0.01 to 500.00 reais)
  const amount = (Math.floor(Math.random() * 49999) + 1) / 100; // 0.01 to 499.99

  // Calculate realistic fees (matching production logic)
  const percentFee = 0; // Most PIX transactions have 0% fee
  const fixedFee = 0.1; // R$ 0.10 fixed fee
  const totalFee = percentFee + fixedFee;
  const netAmount = amount - totalFee;

  // Generate realistic reference code
  const referenceCode = `tx_${Date.now()}_${Math.floor(Math.random() * 999)}`;

  // Base transaction data matching production webhook structure
  const baseTransactionData = {
    id: transactionId,
    type: "CHARGE",
    amount: amount,
    pixKey: null,
    status: "PROCESSING",
    fixedFee: fixedFee,
    totalFee: totalFee,
    createdAt: baseTimestamp,
    netAmount: netAmount,
    paymentAt: null,
    updatedAt: baseTimestamp,
    endToEndId: endToEndId,
    externalId: null,
    percentFee: percentFee,
    pixKeyType: null,
    description: "Produto test 001",
    processedAt: null,
    customerName: "João Silva de Teste",
    customerEmail: "<EMAIL>",
    referenceCode: referenceCode,
    organizationId: organizationId,
    customerDocument: "123.456.789-01"
  };

  // Base webhook payload structure
  const baseWebhookPayload = {
    id: webhookEventId,
    type: eventType,
    created_at: baseTimestamp,
    data: baseTransactionData
  };

  switch (eventType) {
    case WebhookEventType.PIX_IN_PROCESSING:
      return {
        ...baseWebhookPayload,
        data: {
          ...baseTransactionData,
          type: "CHARGE",
          status: "PROCESSING",
          customerName: "João Silva de Teste",
          customerEmail: "<EMAIL>",
          customerDocument: "123.456.789-01",
          description: "Produto test 001"
        }
      };

    case WebhookEventType.PIX_IN_CONFIRMATION:
      const confirmationTimestamp = new Date(now.getTime() + 30000).toISOString(); // 30 seconds later
      return {
        ...baseWebhookPayload,
        data: {
          ...baseTransactionData,
          type: "CHARGE",
          status: "APPROVED",
          previousStatus: "PENDING",
          customerName: "João Silva de Teste",
          customerEmail: "<EMAIL>",
          customerDocument: "123.456.789-01",
          paymentAt: confirmationTimestamp,
          updatedAt: confirmationTimestamp,
          description: "Produto test 001"
        }
      };

    case WebhookEventType.PIX_OUT_PROCESSING:
      // Generate a phone PIX key for outgoing transfers
      const phonePixKey = "+*************";
      const transferExternalId = `mb${Date.now().toString(36)}${Math.random().toString(36).substring(2, 8)}`;

      return {
        ...baseWebhookPayload,
        data: {
          ...baseTransactionData,
          type: "SEND",
          status: "PROCESSING",
          pixKey: phonePixKey,
          pixKeyType: "PHONE",
          customerName: "Transferência PIX",
          customerEmail: `user-${Math.random().toString(36).substring(2, 8)}@pluggou.io`,
          customerDocument: null,
          externalId: transferExternalId,
          referenceCode: null,
          endToEndId: null,
          description: `Transferência PIX para PHONE ${phonePixKey}`
        }
      };

    case WebhookEventType.PIX_OUT_CONFIRMATION:
      const outConfirmationTimestamp = new Date(now.getTime() + 30000).toISOString();
      const outEndToEndId = `E${bankCode}${dateStr}${timeStr}${Math.random().toString(36).substring(2, 15)}`;

      return {
        ...baseWebhookPayload,
        data: {
          ...baseTransactionData,
          type: "SEND",
          status: "APPROVED",
          previousStatus: "PROCESSING",
          pixKey: "+*************",
          pixKeyType: "PHONE",
          customerName: "Transferência PIX",
          customerEmail: `user-${Math.random().toString(36).substring(2, 8)}@pluggou.io`,
          customerDocument: null,
          externalId: `mb${Date.now().toString(36)}${Math.random().toString(36).substring(2, 8)}`,
          referenceCode: null,
          endToEndId: outEndToEndId,
          paymentAt: outConfirmationTimestamp,
          updatedAt: outConfirmationTimestamp,
          description: "Transferência PIX para PHONE +*************"
        }
      };

    case WebhookEventType.PIX_OUT_FAILURE:
      return {
        ...baseWebhookPayload,
        data: {
          ...baseTransactionData,
          type: "SEND",
          status: "REJECTED",
          previousStatus: "PROCESSING",
          pixKey: "+*************",
          pixKeyType: "PHONE",
          customerName: "Transferência PIX",
          customerEmail: `user-${Math.random().toString(36).substring(2, 8)}@pluggou.io`,
          customerDocument: null,
          externalId: `mb${Date.now().toString(36)}${Math.random().toString(36).substring(2, 8)}`,
          referenceCode: null,
          endToEndId: null,
          paymentAt: null,
          description: "Transferência PIX para PHONE +*************"
        }
      };

    case WebhookEventType.PIX_IN_REVERSAL_PROCESSING:
      const originalTransactionId = `cmb${(Date.now() - 3600000).toString(36)}${Math.random().toString(36).substring(2, 8)}`;

      return {
        ...baseWebhookPayload,
        data: {
          ...baseTransactionData,
          type: "REFUND",
          status: "PROCESSING",
          originalTransactionId: originalTransactionId,
          customerName: "João Silva de Teste",
          customerEmail: "<EMAIL>",
          customerDocument: "123.456.789-01",
          description: "Estorno PIX recebido - processando"
        }
      };

    case WebhookEventType.PIX_IN_REVERSAL_CONFIRMATION:
      const reversalConfirmationTimestamp = new Date(now.getTime() + 30000).toISOString();

      return {
        ...baseWebhookPayload,
        data: {
          ...baseTransactionData,
          type: "REFUND",
          status: "APPROVED",
          previousStatus: "PROCESSING",
          originalTransactionId: `cmb${(Date.now() - 3600000).toString(36)}${Math.random().toString(36).substring(2, 8)}`,
          customerName: "João Silva de Teste",
          customerEmail: "<EMAIL>",
          customerDocument: "123.456.789-01",
          paymentAt: reversalConfirmationTimestamp,
          updatedAt: reversalConfirmationTimestamp,
          description: "Estorno PIX recebido - confirmado"
        }
      };

    case WebhookEventType.PIX_OUT_REVERSAL:
      const outReversalTimestamp = new Date(now.getTime() + 30000).toISOString();

      return {
        ...baseWebhookPayload,
        data: {
          ...baseTransactionData,
          type: "REFUND",
          status: "APPROVED",
          originalTransactionId: `cmb${(Date.now() - 3600000).toString(36)}${Math.random().toString(36).substring(2, 8)}`,
          pixKey: "<EMAIL>",
          pixKeyType: "EMAIL",
          customerName: "Maria Santos de Teste",
          customerEmail: "<EMAIL>",
          customerDocument: "987.654.321-00",
          paymentAt: outReversalTimestamp,
          updatedAt: outReversalTimestamp,
          description: "Estorno PIX enviado - processado"
        }
      };

    default:
      return baseWebhookPayload;
  }
}
