import { NextRequest, NextResponse } from "next/server";
import { auth } from "@repo/auth";
import { getWebhookEventDetails } from "@repo/payments/src/webhooks/svix-portal-service";
import { logger } from "@repo/logs";

export async function GET(
  req: NextRequest,
  { params }: { params: { messageId: string } }
) {
  try {
    const session = await auth.api.getSession({ headers: req.headers });
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { messageId } = params;

    if (!messageId) {
      return NextResponse.json({ error: "Message ID required" }, { status: 400 });
    }

    logger.info("Fetching webhook event details", {
      userId: session.user.id,
      messageId
    });

    const eventDetails = await getWebhookEventDetails(messageId);

    return NextResponse.json({
      success: true,
      data: eventDetails
    });
  } catch (error) {
    logger.error("Failed to fetch webhook event details", {
      error: error instanceof Error ? error.message : String(error),
      messageId: params.messageId
    });

    return NextResponse.json(
      { error: "Failed to fetch webhook event details" },
      { status: 500 }
    );
  }
}
