import { NextRequest, NextResponse } from 'next/server';
import { getOwempayV2Balance } from '@repo/payments/provider/owempayv2';
import { logger } from '@repo/logs';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { gatewayId, organizationId } = body;

    if (!gatewayId || !organizationId) {
      return NextResponse.json(
        { error: 'Gateway ID e Organization ID são obrigatórios' },
        { status: 400 }
      );
    }

    // Por enquanto, só suportamos Owempay V2
    // TODO: Adicionar suporte para outros gateways que tenham saldo
    // O gatewayId é o ID da instância, precisamos verificar o tipo do gateway
    // Por enquanto, assumimos que se chegou até aqui, é um gateway Owempay V2
    // TODO: Implementar verificação do tipo do gateway no banco de dados

    logger.info('Buscando saldo do gateway', { gatewayId, organizationId });

    const balanceData = await getOwempayV2Balance(organizationId);

    return NextResponse.json({
      success: true,
      data: balanceData.data,
    });
  } catch (error) {
    logger.error('Erro ao buscar saldo do gateway', { error });

    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : 'Erro interno do servidor'
      },
      { status: 500 }
    );
  }
}
