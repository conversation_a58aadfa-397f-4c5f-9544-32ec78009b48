import { auth } from "@repo/auth";
import { db } from "@repo/database";
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { logger } from "@repo/logs";
import { checkWithdrawalBlocking } from "@repo/utils";

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const headersList = await headers();
    const session = await auth.api.getSession({
      headers: headersList,
    });

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Não autorizado" }, { status: 401 });
    }

    const { id: organizationId } = await params;

    // Verificar se o usuário tem acesso à organização
    const membership = await db.member.findFirst({
      where: {
        userId: session.user.id,
        organizationId: organizationId
      }
    });

    if (!membership) {
      return NextResponse.json({ message: "Acesso negado. Você não tem permissão para acessar esta organização." }, { status: 403 });
    }

    // Use the shared withdrawal blocking utility
    const blockingResult = await checkWithdrawalBlocking(organizationId);

    return NextResponse.json(blockingResult);

  } catch (error) {
    logger.error("Erro ao verificar bloqueio de saques", { error, organizationId: params });
    return NextResponse.json({
      blocked: false,
      message: "Erro interno do servidor"
    }, { status: 500 });
  }
}
