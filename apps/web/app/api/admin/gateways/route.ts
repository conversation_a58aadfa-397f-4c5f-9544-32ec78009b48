import { getSession } from "@saas/auth/lib/server";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { NextRequest, NextResponse } from "next/server";
import { Zod<PERSON>rror, z } from "zod";
import { auth } from "@repo/auth";
import { headers } from "next/headers";
import { createId } from "@paralleldrive/cuid2";

// Schema for validating gateway data
const gatewaySchema = z.object({
  type: z.string(),
  displayName: z.string().optional(),
  description: z.string().optional(),
  instanceNumber: z.coerce.number().int().min(1).optional(),
  isActive: z.boolean().default(false),
  isDefault: z.boolean().default(false),
  priority: z.coerce.number().int().min(0).default(999),
  credentials: z.record(z.any()).default({}),
  canReceive: z.boolean().default(true),
  canSend: z.boolean().default(false),
  pixChargePercentFee: z.coerce.number().min(0).max(100).default(0),
  pixTransferPercentFee: z.coerce.number().min(0).max(100).default(0),
  pixChargeFixedFee: z.coerce.number().min(0).default(0),
  pixTransferFixedFee: z.coerce.number().min(0).default(0),
});

export async function GET(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });

    if (!session) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if user is an admin
    const userData = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (userData?.role !== "admin") {
      return NextResponse.json(
        { error: "Forbidden. Admin access required." },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("query") || "";
    const limit = parseInt(searchParams.get("limit") || "10", 10);
    const offset = parseInt(searchParams.get("offset") || "0", 10);
    const organizationId = searchParams.get("organizationId") || "global";
    const type = searchParams.get("type") || "";

    // Build where clause
    const whereClause: any = {};

    if (query) {
      whereClause.name = { contains: query, mode: "insensitive" };
    }

    // Filter by gateway type if specified
    if (type) {
      whereClause.type = { equals: type, mode: "insensitive" };
    }

    // Get global or specific organization gateways
    if (organizationId !== "global") {
      // Para gateways específicos de uma organização, usamos a relação many-to-many
      whereClause.OR = [
        {
          organizations: {
            some: {
              organizationId
            }
          }
        },
        { isGlobal: true }
      ];
    } else {
      // Apenas gateways globais
      whereClause.isGlobal = true;
    }

    // Get gateways
    const gateways = await db.payment_gateway.findMany({
      where: whereClause,
      take: limit,
      skip: offset,
      orderBy: [
        { type: 'asc' },
        { instanceNumber: 'asc' },
        { priority: 'asc' },
      ],
      select: {
        id: true,
        type: true,
        name: true,
        displayName: true,
        description: true,
        instanceNumber: true,
        isActive: true,
        isDefault: true,
        isGlobal: true,
        priority: true,
        canReceive: true,
        canSend: true,
        credentials: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    // Count total
    const total = await db.payment_gateway.count({
      where: whereClause,
    });

    // If type is specified, also return instance information
    let instanceInfo = null;
    if (type) {
      const instances = await db.payment_gateway.findMany({
        where: { type: { equals: type, mode: "insensitive" } },
        select: { instanceNumber: true, displayName: true, isActive: true },
        orderBy: { instanceNumber: 'asc' }
      });

      instanceInfo = {
        type: type,
        totalInstances: instances.length,
        instances: instances,
        nextInstanceNumber: instances.length > 0 ? Math.max(...instances.map(i => i.instanceNumber)) + 1 : 1
      };
    }

    return NextResponse.json({
      gateways,
      total,
      instanceInfo
    });
  } catch (error) {
    console.error("Error fetching gateways:", error);
    return NextResponse.json(
      { error: "Failed to fetch gateways" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação do usuário
    const session = await getSession();
    if (!session?.user || !session.user.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verificar se o usuário é admin
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true },
    });

    if (user?.role !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized: Admin access required" },
        { status: 403 }
      );
    }

    // Receber e validar os dados do gateway
    const body = await request.json();
    console.log("📝 Dados recebidos:", JSON.stringify(body, null, 2));

    const validationResult = gatewaySchema.safeParse(body);

    if (!validationResult.success) {
      console.log("❌ Erro de validação Zod:", validationResult.error.issues);
      return NextResponse.json(
        { error: "Dados inválidos", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const gatewayData = validationResult.data;
    console.log("✅ Dados validados:", JSON.stringify(gatewayData, null, 2));

    // Validações específicas por tipo de gateway
    if (gatewayData.type.toLowerCase() === 'mediuspag') {
      // Verificar se companyId está presente nas credenciais para MediusPag
      if (!gatewayData.credentials.companyId) {
        return NextResponse.json(
          { error: "O campo Company ID é obrigatório para o gateway MediusPag" },
          { status: 400 }
        );
      }
    }

    if (gatewayData.type.toLowerCase() === 'zendry') {
      console.log("🔍 Validando credenciais Zendry");
      // Verificar se clientId e clientSecret estão presentes nas credenciais para Zendry
      if (!gatewayData.credentials.clientId || !gatewayData.credentials.clientSecret) {
        console.log("❌ Faltam clientId ou clientSecret");
        return NextResponse.json(
          { error: "Os campos Client ID e Client Secret são obrigatórios para o gateway Zendry" },
          { status: 400 }
        );
      }
      // Verificar se environment está presente
      if (!gatewayData.credentials.environment) {
        console.log("❌ Falta environment");
        return NextResponse.json(
          { error: "O campo Ambiente é obrigatório para o gateway Zendry" },
          { status: 400 }
        );
      }
      console.log("✅ Credenciais Zendry válidas");
    }

    if (gatewayData.type.toLowerCase() === 'ecomovi') {
      console.log("🔍 Validando credenciais Ecomovi");
      // Verificar se clientId, clientSecret, pixKey e environment estão presentes
      if (!gatewayData.credentials.clientId) {
        console.log("❌ Falta clientId");
        return NextResponse.json(
          { error: "O campo Client ID é obrigatório para o gateway Ecomovi" },
          { status: 400 }
        );
      }
      if (!gatewayData.credentials.clientSecret) {
        console.log("❌ Falta clientSecret");
        return NextResponse.json(
          { error: "O campo Client Secret é obrigatório para o gateway Ecomovi" },
          { status: 400 }
        );
      }
      if (!gatewayData.credentials.pixKey) {
        console.log("❌ Falta pixKey");
        return NextResponse.json(
          { error: "O campo Chave PIX é obrigatório para o gateway Ecomovi" },
          { status: 400 }
        );
      }
      if (!gatewayData.credentials.environment) {
        console.log("❌ Falta environment");
        return NextResponse.json(
          { error: "O campo Ambiente é obrigatório para o gateway Ecomovi" },
          { status: 400 }
        );
      }
      console.log("✅ Credenciais Ecomovi válidas");
    }

    if (gatewayData.type.toLowerCase() === 'cartwave') {
      console.log("🔍 Validando credenciais Cartwave");
      // Verificar se apiKey está presente
      if (!gatewayData.credentials.apiKey) {
        console.log("❌ Falta apiKey");
        return NextResponse.json(
          { error: "O campo API Key é obrigatório para o gateway Cartwave" },
          { status: 400 }
        );
      }
      // Environment é opcional, mas se fornecido deve ser válido
      if (gatewayData.credentials.environment &&
          !['sandbox', 'production'].includes(gatewayData.credentials.environment)) {
        console.log("❌ Environment inválido");
        return NextResponse.json(
          { error: "O campo Ambiente deve ser 'sandbox' ou 'production' para o gateway Cartwave" },
          { status: 400 }
        );
      }
      console.log("✅ Credenciais Cartwave válidas");
    }

    if (gatewayData.type.toLowerCase() === 'pagarme') {
      console.log("🔍 Validando credenciais Pagar.me");
      // Verificar se apiKey está presente
      if (!gatewayData.credentials.apiKey) {
        console.log("❌ Falta apiKey");
        return NextResponse.json(
          { error: "O campo API Key é obrigatório para o gateway Pagar.me" },
          { status: 400 }
        );
      }
      // Verificar se environment está presente
      if (!gatewayData.credentials.environment) {
        console.log("❌ Falta environment");
        return NextResponse.json(
          { error: "O campo Ambiente é obrigatório para o gateway Pagar.me" },
          { status: 400 }
        );
      }
      // Verificar se environment é válido
      if (!['sandbox', 'production'].includes(gatewayData.credentials.environment)) {
        console.log("❌ Environment inválido");
        return NextResponse.json(
          { error: "O campo Ambiente deve ser 'sandbox' ou 'production' para o gateway Pagar.me" },
          { status: 400 }
        );
      }
      console.log("✅ Credenciais Pagar.me válidas");
    }

    // Verificar se a instância já existe
    if (gatewayData.instanceNumber) {
      const existingInstance = await db.payment_gateway.findFirst({
        where: {
          type: gatewayData.type,
          instanceNumber: gatewayData.instanceNumber,
        },
      });

      if (existingInstance) {
        return NextResponse.json(
          { error: `A instância ${gatewayData.instanceNumber} já existe para o gateway ${gatewayData.type}` },
          { status: 400 }
        );
      }
    }

    // Calculate next instance number for this gateway type
    const existingInstances = await db.payment_gateway.findMany({
      where: {
        type: gatewayData.type,
      },
      select: {
        instanceNumber: true,
      },
      orderBy: {
        instanceNumber: 'desc',
      },
    });

    const nextInstanceNumber = gatewayData.instanceNumber ||
      (existingInstances.length > 0 ? existingInstances[0].instanceNumber + 1 : 1);

    // Se isDefault for true, desmarcar outros gateways como padrão
    if (gatewayData.isDefault) {
      await db.payment_gateway.updateMany({
        where: {
          isDefault: true,
        },
        data: {
          isDefault: false,
        },
      });
    }

    // Generate displayName if not provided
    const finalDisplayName = gatewayData.displayName ||
      (nextInstanceNumber > 1
        ? `${getGatewayName(gatewayData.type)} (${nextInstanceNumber})`
        : getGatewayName(gatewayData.type));

    console.log('Creating gateway with data:', {
      ...gatewayData,
      instanceNumber: nextInstanceNumber,
      displayName: finalDisplayName
    });

    // Criar o gateway
    console.log("🚀 Tentando criar gateway com dados:", {
      name: getGatewayName(gatewayData.type),
      displayName: finalDisplayName,
      description: gatewayData.description,
      instanceNumber: nextInstanceNumber,
      type: gatewayData.type,
      isActive: gatewayData.isActive,
      isDefault: gatewayData.isDefault,
      priority: gatewayData.priority,
      credentials: gatewayData.credentials,
      canReceive: gatewayData.canReceive,
      canSend: gatewayData.canSend,
      isGlobal: true,
    });

    const gateway = await db.payment_gateway.create({
      data: {
        id: createId(),
        name: getGatewayName(gatewayData.type),
        displayName: finalDisplayName,
        description: gatewayData.description,
        instanceNumber: nextInstanceNumber,
        type: gatewayData.type,
        isActive: gatewayData.isActive,
        isDefault: gatewayData.isDefault,
        priority: gatewayData.priority,
        credentials: gatewayData.credentials,
        canReceive: gatewayData.canReceive,
        canSend: gatewayData.canSend,
        isGlobal: true, // Definindo como gateway global
      },
    });

    console.log("✅ Gateway criado com sucesso:", gateway.id);

    return NextResponse.json(gateway, { status: 201 });
  } catch (error) {
    logger.error("Error creating gateway:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });

    // Mensagens de erro mais específicas
    if (error instanceof Error) {
      if (error.message.includes("organizationId")) {
        return NextResponse.json(
          { error: "Erro no campo organizationId. Este campo pode ter sido removido ou alterado no esquema." },
          { status: 500 }
        );
      }

      if (error.message.includes("credentials")) {
        return NextResponse.json(
          { error: "Erro nas credenciais do gateway. Verifique se todos os campos obrigatórios foram fornecidos." },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Erro interno ao criar o gateway" },
      { status: 500 }
    );
  }
}

// Função auxiliar para obter o nome do gateway com base no tipo
function getGatewayName(type: string): string {
  const gatewayTypes: Record<string, string> = {
    flow2pay: "Flow2Pay",
    mercadopago: "Mercado Pago",
    asaas: "Asaas",
    reflowpay: "ReflowPay",
    primepag: "PrimePag",
    pixium: "Pixium",
    transfeera: "Transfeera",
    mediuspag: "MediusPag",
    pluggou_pix: "Pluggou PIX",
    zendry: "Zendry",
    ecomovi: "Ecomovi",
    cartwave: "Cartwave Hub",
    pagarme: "Pagar.me",
    owempay: "Owempay",
  };

  return gatewayTypes[type.toLowerCase()] || type;
}
