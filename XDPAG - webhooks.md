POST
Cashin Finished
{{yourUrl}
Body
raw (json)
{
    "type": "PAYIN",
    "data": {
        "id": "f466fef7-bad2-4ff3-a50f-bd95cf2bbbf6",
        "externalId": "f466fef7-bad2-4ff3-a50f-bd95cf2bbbf6",
        "amount": "20",
        "document": "",
        "original_amount": "20",
        "status": "FINISHED",
        "endToEndId": "E********202412271326s1360618214",
        "receipt": "",
        "fee": "0.06",
        "metadata": [],
        "refunds": []
    }
}


--------


POST
Cashout Finished
{{yourUrl}
Body
raw (json)


{
    "type": "PAYOUT",
    "data": {
        "id": "577b7e1c-248c-4b1e-a6d8-e79e24c4ec8e",
        "externalId": "b909a9b0-91f3-4488-b3d4-e314aeb0b75b",
        "amount": "1",
        "document": "***********",
        "original_amount": "1",
        "status": "FINISHED",
        "endToEndId": "E5440356320241128165123v3Na7HbBl",
        "receipt": "https://api.xdpag.com/receipt/E5440356320241128165123v3Na7HbBl/payout",
        "fee": "0.00",
        "metadata": {
            "authCode": "577b7e1c-248c-4b1e-a6d8-e79e24c4ec8e",
            "amount": "1",
            "paymentDateTime": "2024-11-28T13:53:56.000Z",
            "pixKey": "***********",
            "receiveName": "Lucas Araujo",
            "receiverName": "Lucas Araujo",
            "receiverBankName": "********",
            "receiverDocument": "***********",
            "receiveAgency": "1",
            "receiveAccount": "********",
            "payerName": "VITALCRED MEIOS DE PAGAMENTOS SA",
            "payerAgency": "001",
            "payerAccount": "38805-7",
            "payerDocument": "**************",
            "payerBankName": "BCO ARBI S.A.",
            "createdAt": "2024-11-28T16:53:50.000000Z",
            "endToEnd": "E5440356320241128165123v3Na7HbBl"
        },
        "reason_cancelled": ""
    }
}



